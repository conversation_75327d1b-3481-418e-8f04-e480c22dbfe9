from typing import Optional, List, Any, Dict
from datetime import datetime, timezone

from sqlalchemy import Column, String, <PERSON><PERSON><PERSON>, DateTime, BigInteger, Integer
from sqlalchemy.orm import relationship, Mapped, mapped_column

from svc.core.database.session import Base
from svc.core.security.password import get_password_hash
from svc.core.utils.datetime_utils import get_utc_now_without_tzinfo
from .role import Role
from .user_role import user_role
from .wechat_user import WechatUser






class User(Base):
    """用户模型"""
    __tablename__ = "users"
    
    id: Mapped[int] = mapped_column(Integer, primary_key=True, autoincrement=True)
    username: Mapped[str] = mapped_column(String(50), unique=True, index=True, nullable=False)
    email: Mapped[str] = mapped_column(String(120), unique=True, index=True, nullable=False)
    hashed_password: Mapped[str] = mapped_column("password", String(255), nullable=False)  # 映射到数据库的password列
    fullname: Mapped[str] = mapped_column("full_name", String(100))  # 映射到数据库的full_name列
    is_active: Mapped[bool] = mapped_column(Boolean, default=True)
    is_superuser: Mapped[bool] = mapped_column(Boolean, default=False)
    
    # 时间戳
    created_at: Mapped[datetime] = mapped_column(
        DateTime(timezone=True),
        default=get_utc_now_without_tzinfo,
        nullable=False
    )
    updated_at: Mapped[datetime] = mapped_column(
        DateTime(timezone=True),
        default=get_utc_now_without_tzinfo,
        onupdate=get_utc_now_without_tzinfo,
        nullable=False
    )
    last_login: Mapped[Optional[datetime]] = mapped_column(DateTime(timezone=True))
    
    # 用户配置
    avatar_url: Mapped[Optional[str]] = mapped_column(String(255))
    locale: Mapped[str] = mapped_column(String(10), default="zh-CN")
    timezone: Mapped[str] = mapped_column(String(30), default="Asia/Shanghai")
    
    # 安全相关
    password_changed_at: Mapped[Optional[datetime]] = mapped_column(DateTime(timezone=True))
    failed_login_attempts: Mapped[int] = mapped_column(Integer, default=0)
    locked_until: Mapped[Optional[datetime]] = mapped_column(DateTime(timezone=True))
    
    # 关联角色
    roles = relationship(
        "Role",
        secondary=user_role,
        back_populates="users",
        lazy="dynamic"
    )
    
    # 关联微信用户
    wechat_users = relationship("WechatUser", back_populates="user")
    
    def __repr__(self) -> str:
        return f"<User {self.username}>"
    
    @property
    def is_locked(self) -> bool:
        """用户是否被锁定"""
        if not self.locked_until:
            return False
        return datetime.now(timezone.utc) < self.locked_until
    
    def record_login_attempt(self, success: bool):
        """记录登录尝试"""
        if success:
            self.failed_login_attempts = 0
            self.last_login = get_utc_now_without_tzinfo()
            self.locked_until = None
        else:
            self.failed_login_attempts += 1
            if self.failed_login_attempts >= 5:  # 5次失败后锁定
                from datetime import timedelta
                self.locked_until = get_utc_now_without_tzinfo() + timedelta(minutes=30)
    
    def update_password(self, new_password: str):
        """更新密码"""
        self.hashed_password = get_password_hash(new_password)
        self.password_changed_at = get_utc_now_without_tzinfo()
        self.failed_login_attempts = 0
        self.locked_until = None
    
    def to_dict(self, role_names: List[dict] = None) -> Dict[str, Any]:
        """
        将用户对象转换为字典，用于JSON序列化和缓存
        
        Args:
            role_names: 用户的角色名称列表，由外部传入以避免直接访问self.roles
            
        Returns:
            Dict[str, Any]: 用户的字典表示
        """
        return {
            "id": self.id,
            "username": self.username,
            "email": self.email,
            "fullname": self.fullname,
            "is_active": self.is_active,
            "is_superuser": self.is_superuser,
            "created_at": self.created_at.isoformat() if self.created_at else None,
            "updated_at": self.updated_at.isoformat() if self.updated_at else None,
            "last_login": self.last_login.isoformat() if self.last_login else None,
            "avatar_url": self.avatar_url,
            "locale": self.locale,
            "timezone": self.timezone,
            "failed_login_attempts": self.failed_login_attempts,
            "locked_until": self.locked_until.isoformat() if self.locked_until else None,
            "password_changed_at": self.password_changed_at.isoformat() if self.password_changed_at else None,
            "is_locked": self.is_locked,
            "roles": role_names or []
        } 