"""
系统服务实现。
提供系统级功能的业务逻辑实现，如配置管理、系统监控等。
"""

from typing import Optional, List, Dict, Any, Tuple
import logging
from datetime import datetime

from sqlalchemy.ext.asyncio import AsyncSession
from redis.asyncio import Redis
from fastapi import Request

from svc.core.services import BaseService
from svc.core.services.result import Result, ResultFactory
from svc.core.exceptions.error_codes import ErrorCode
from svc.apps.system.repositories.config import ConfigRepository, ConfigItem
from svc.apps.system.repositories.audit_log import AuditLogRepository

class SystemService(BaseService[ConfigItem, Result[Dict[str, Any]]]):
    """
    系统服务类，提供系统级功能的实现
    
    属性:
        resource_type: 资源类型名称
        config_repo: 配置仓库实例
        audit_log_repo: 审计日志仓库实例
    """
    
    # 设置资源类型名称
    resource_type = "system"
    
    def __init__(
        self, 
        db: AsyncSession, 
        redis: Optional[Redis] = None,
        config_repo: Optional[ConfigRepository] = None,
        audit_log_repo: Optional[AuditLogRepository] = None
    ):
        """初始化系统服务
        
        Args:
            db: 数据库会话
            redis: Redis客户端（可选）
            config_repo: 配置仓库实例，不提供则创建新实例
            audit_log_repo: 审计日志仓库实例，不提供则创建新实例
        """
        super().__init__(db, redis)
        self.logger = logging.getLogger(__name__)
        self.config_repo = config_repo or ConfigRepository()
        self.audit_log_repo = audit_log_repo or AuditLogRepository()
    
    async def get_system_info(self) -> Result[Dict[str, Any]]:
        """
        获取系统信息
        
        Returns:
            Result[Dict[str, Any]]: 系统信息
        """
        try:
            # 模拟系统信息收集
            info = {
                "name": "AI Tools Service",
                "version": "0.1.0",
                "status": "running",
                "uptime": "1d 2h 34m",  # 实际应用中应动态计算
                "cpu_usage": "12%",      # 实际应用中应动态获取
                "memory_usage": "256MB", # 实际应用中应动态获取
            }
            
            return ResultFactory.success(info)
        except Exception as e:
            self.logger.error(f"获取系统信息失败: {str(e)}", exc_info=True)
            return ResultFactory.error(ErrorCode.INTERNAL_ERROR, f"获取系统信息失败: {str(e)}")
    
    async def get_health_status(self) -> Result[Dict[str, Any]]:
        """
        获取系统健康状态
        
        Returns:
            Result[Dict[str, Any]]: 健康状态信息
        """
        try:
            # 模拟健康检查
            services_status = {
                "database": "healthy",
                "redis": "healthy" if self.redis else "not_configured",
                "api": "healthy",
            }
            
            status = "healthy"  # 如果所有服务都健康，则整体健康
            
            # 如果有服务不健康，则整体状态为不健康
            if "unhealthy" in services_status.values():
                status = "unhealthy"
            
            result = {
                "status": status,
                "services": services_status,
                "timestamp": "2023-09-24T15:30:45Z",  # 实际应用中应使用当前时间
            }
            
            return ResultFactory.success(result)
        except Exception as e:
            self.logger.error(f"健康检查失败: {str(e)}", exc_info=True)
            return ResultFactory.error(ErrorCode.INTERNAL_ERROR, f"健康检查失败: {str(e)}")
    
    async def get_config(self, config_id: int) -> Result[Dict[str, Any]]:
        """
        获取配置项
        
        Args:
            config_id: 配置项ID
            
        Returns:
            Result[Dict[str, Any]]: 配置项
        """
        try:
            config_item = await self.config_repo.get_by_id(self.db, config_id)
            if not config_item:
                return ResultFactory.error(
                    ErrorCode.NOT_FOUND, 
                    f"配置项(ID={config_id})不存在"
                )
                
            return ResultFactory.success(config_item.to_dict())
        except Exception as e:
            self.logger.error(f"获取配置项失败: {str(e)}", exc_info=True)
            return ResultFactory.error(ErrorCode.INTERNAL_ERROR, f"获取配置项失败: {str(e)}")
    
    async def get_configs(self, prefix: Optional[str] = None) -> Result[List[Dict[str, Any]]]:
        """
        获取配置项列表
        
        Args:
            prefix: 配置项键前缀（可选）
            
        Returns:
            Result[List[Dict[str, Any]]]: 配置项列表
        """
        try:
            config_items = await self.config_repo.get_by_prefix(self.db, prefix or "")
            return ResultFactory.success([item.to_dict() for item in config_items])
        except Exception as e:
            self.logger.error(f"获取配置项列表失败: {str(e)}", exc_info=True)
            return ResultFactory.error(ErrorCode.INTERNAL_ERROR, f"获取配置项列表失败: {str(e)}")
    
    async def create_config(
        self, 
        data: Dict[str, Any], 
        user_id: Optional[int] = None,
        username: Optional[str] = None,
        request: Optional[Request] = None
    ) -> Result[Dict[str, Any]]:
        """
        创建配置项
        
        Args:
            data: 配置项数据
            user_id: 执行操作的用户ID
            username: 执行操作的用户名
            request: 请求对象，用于获取IP和User-Agent
            
        Returns:
            Result[Dict[str, Any]]: 创建的配置项
        """
        try:
            # 检查键是否已存在
            existing_items = await self.config_repo.get_by_prefix(self.db, data["key"])
            for item in existing_items:
                if item.key == data["key"]:
                    return ResultFactory.error(
                        ErrorCode.ALREADY_EXISTS, 
                        f"配置项键'{data['key']}'已存在"
                    )
            
            # 创建配置项
            config_item = await self.config_repo.create(self.db, data)
            
            # 记录审计日志
            await self._log_audit_action(
                action="create",
                resource_type="config",
                resource_id=str(config_item.id),
                user_id=user_id,
                username=username,
                request=request,
                details={"key": data.get("key"), "description": data.get("description")},
                status="success"
            )
            
            return ResultFactory.success(config_item.to_dict())
        except Exception as e:
            # 记录审计日志（失败）
            await self._log_audit_action(
                action="create",
                resource_type="config",
                resource_id=None,
                user_id=user_id,
                username=username,
                request=request,
                details={"key": data.get("key"), "error": str(e)},
                status="failure",
                message=f"创建配置项失败: {str(e)}"
            )
            
            self.logger.error(f"创建配置项失败: {str(e)}", exc_info=True)
            return ResultFactory.error(ErrorCode.INTERNAL_ERROR, f"创建配置项失败: {str(e)}")
    
    async def update_config(
        self, 
        config_id: int, 
        data: Dict[str, Any],
        user_id: Optional[int] = None,
        username: Optional[str] = None,
        request: Optional[Request] = None
    ) -> Result[Dict[str, Any]]:
        """
        更新配置项
        
        Args:
            config_id: 配置项ID
            data: 配置项数据
            user_id: 执行操作的用户ID
            username: 执行操作的用户名
            request: 请求对象，用于获取IP和User-Agent
            
        Returns:
            Result[Dict[str, Any]]: 更新后的配置项
        """
        try:
            # 检查配置项是否存在
            existing_item = await self.config_repo.get_by_id(self.db, config_id)
            if not existing_item:
                return ResultFactory.error(
                    ErrorCode.NOT_FOUND, 
                    f"配置项(ID={config_id})不存在"
                )
            
            # 记录更新前的值，用于审计
            old_values = existing_item.to_dict()
            
            # 更新配置项
            updated_item = await self.config_repo.update(self.db, config_id, data)
            
            # 记录审计日志
            await self._log_audit_action(
                action="update",
                resource_type="config",
                resource_id=str(config_id),
                user_id=user_id,
                username=username,
                request=request,
                details={
                    "old_values": old_values,
                    "new_values": updated_item.to_dict(),
                    "changed_fields": list(data.keys())
                },
                status="success"
            )
            
            return ResultFactory.success(updated_item.to_dict())
        except Exception as e:
            # 记录审计日志（失败）
            await self._log_audit_action(
                action="update",
                resource_type="config",
                resource_id=str(config_id),
                user_id=user_id,
                username=username,
                request=request,
                details={"error": str(e)},
                status="failure",
                message=f"更新配置项失败: {str(e)}"
            )
            
            self.logger.error(f"更新配置项失败: {str(e)}", exc_info=True)
            return ResultFactory.error(ErrorCode.INTERNAL_ERROR, f"更新配置项失败: {str(e)}")
    
    async def delete_config(
        self, 
        config_id: int,
        user_id: Optional[int] = None,
        username: Optional[str] = None,
        request: Optional[Request] = None
    ) -> Result[Dict[str, Any]]:
        """
        删除配置项
        
        Args:
            config_id: 配置项ID
            user_id: 执行操作的用户ID
            username: 执行操作的用户名
            request: 请求对象，用于获取IP和User-Agent
            
        Returns:
            Result[Dict[str, Any]]: 删除结果
        """
        try:
            # 检查配置项是否存在
            existing_item = await self.config_repo.get_by_id(self.db, config_id)
            if not existing_item:
                return ResultFactory.error(
                    ErrorCode.NOT_FOUND, 
                    f"配置项(ID={config_id})不存在"
                )
            
            # 记录删除前的值，用于审计
            old_values = existing_item.to_dict()
            
            # 删除配置项
            result = await self.config_repo.delete(self.db, config_id)
            if result:
                # 记录审计日志
                await self._log_audit_action(
                    action="delete",
                    resource_type="config",
                    resource_id=str(config_id),
                    user_id=user_id,
                    username=username,
                    request=request,
                    details={"deleted_item": old_values},
                    status="success"
                )
                
                return ResultFactory.success({"id": config_id, "deleted": True})
            else:
                # 记录审计日志（失败）
                await self._log_audit_action(
                    action="delete",
                    resource_type="config",
                    resource_id=str(config_id),
                    user_id=user_id,
                    username=username,
                    request=request,
                    details={"error": "Unknown error during deletion"},
                    status="failure",
                    message=f"删除配置项(ID={config_id})失败"
                )
                
                return ResultFactory.error(
                    ErrorCode.OPERATION_FAILED, 
                    f"删除配置项(ID={config_id})失败"
                )
        except Exception as e:
            # 记录审计日志（失败）
            await self._log_audit_action(
                action="delete",
                resource_type="config",
                resource_id=str(config_id),
                user_id=user_id,
                username=username,
                request=request,
                details={"error": str(e)},
                status="failure",
                message=f"删除配置项失败: {str(e)}"
            )
            
            self.logger.error(f"删除配置项失败: {str(e)}", exc_info=True)
            return ResultFactory.error(ErrorCode.INTERNAL_ERROR, f"删除配置项失败: {str(e)}")
    
    async def update_configs_by_prefix(
        self, 
        prefix: str, 
        new_value: str,
        user_id: Optional[int] = None,
        username: Optional[str] = None,
        request: Optional[Request] = None
    ) -> Result[Dict[str, Any]]:
        """
        根据前缀批量更新配置项
        
        Args:
            prefix: 配置项键前缀
            new_value: 新值
            user_id: 执行操作的用户ID
            username: 执行操作的用户名
            request: 请求对象，用于获取IP和User-Agent
            
        Returns:
            Result[Dict[str, Any]]: 更新结果
        """
        try:
            if not prefix:
                return ResultFactory.error(
                    ErrorCode.INVALID_PARAMS, 
                    "前缀不能为空"
                )
            
            # 获取所有匹配前缀的配置
            items_before = await self.config_repo.get_by_prefix(self.db, prefix)
            if not items_before:
                return ResultFactory.error(
                    ErrorCode.NOT_FOUND, 
                    f"没有找到前缀为'{prefix}'的配置项"
                )
            
            # 批量更新
            count, updated_items = await self.config_repo.update_by_prefix(
                self.db, prefix, {"value": new_value}
            )
            
            # 记录审计日志
            await self._log_audit_action(
                action="batch_update",
                resource_type="config",
                resource_id=f"prefix:{prefix}",
                user_id=user_id,
                username=username,
                request=request,
                details={
                    "prefix": prefix,
                    "new_value": new_value,
                    "affected_count": count,
                    "affected_keys": [item.key for item in items_before]
                },
                status="success",
                message=f"批量更新了{count}个配置项"
            )
            
            # 返回结果
            return ResultFactory.success({
                "updated_count": count,
                "items": [item.to_dict() for item in updated_items]
            })
        except Exception as e:
            # 记录审计日志（失败）
            await self._log_audit_action(
                action="batch_update",
                resource_type="config",
                resource_id=f"prefix:{prefix}",
                user_id=user_id,
                username=username,
                request=request,
                details={"error": str(e), "prefix": prefix},
                status="failure",
                message=f"批量更新配置项失败: {str(e)}"
            )
            
            self.logger.error(f"批量更新配置项失败: {str(e)}", exc_info=True)
            return ResultFactory.error(ErrorCode.INTERNAL_ERROR, f"批量更新配置项失败: {str(e)}")
    
    # 审计日志相关方法
    
    async def _log_audit_action(
        self,
        action: str,
        resource_type: str,
        resource_id: Optional[str] = None,
        user_id: Optional[int] = None,
        username: Optional[str] = None,
        request: Optional[Request] = None,
        details: Optional[Dict[str, Any]] = None,
        status: str = "success",
        message: Optional[str] = None
    ) -> None:
        """
        记录审计日志
        
        Args:
            action: 操作类型
            resource_type: 资源类型
            resource_id: 资源ID
            user_id: 执行操作的用户ID
            username: 执行操作的用户名
            request: 请求对象，用于获取IP和User-Agent
            details: 操作详情
            status: 操作结果，success或failure
            message: 额外信息
        """
        try:
            # 获取IP和User-Agent
            ip_address = None
            user_agent = None
            if request:
                ip_address = request.client.host if hasattr(request, 'client') else None
                user_agent = request.headers.get("user-agent")
            
            # 创建审计日志
            log_data = {
                "action": action,
                "resource_type": resource_type,
                "resource_id": resource_id,
                "user_id": user_id,
                "username": username,
                "ip_address": ip_address,
                "user_agent": user_agent,
                "status": status,
                "details": details,
                "message": message
            }
            
            await self.audit_log_repo.create(self.db, log_data)
        except Exception as e:
            # 审计日志创建失败不应影响主要业务逻辑，只记录错误日志
            self.logger.error(f"记录审计日志失败: {str(e)}", exc_info=True)
    
    async def get_audit_log(self, log_id: int) -> Result[Dict[str, Any]]:
        """
        获取审计日志详情
        
        Args:
            log_id: 审计日志ID
            
        Returns:
            Result[Dict[str, Any]]: 审计日志详情
        """
        try:
            log = await self.audit_log_repo.get_by_id(self.db, log_id)
            if not log:
                return ResultFactory.error(
                    ErrorCode.NOT_FOUND, 
                    f"审计日志(ID={log_id})不存在"
                )
            
            return ResultFactory.success(log.to_dict())
        except Exception as e:
            self.logger.error(f"获取审计日志失败: {str(e)}", exc_info=True)
            return ResultFactory.error(ErrorCode.INTERNAL_ERROR, f"获取审计日志失败: {str(e)}")
    
    async def search_audit_logs(
        self,
        action: Optional[str] = None,
        resource_type: Optional[str] = None,
        resource_id: Optional[str] = None,
        user_id: Optional[int] = None,
        username: Optional[str] = None,
        status: Optional[str] = None,
        start_date: Optional[datetime] = None,
        end_date: Optional[datetime] = None,
        page: int = 1,
        page_size: int = 20
    ) -> Result[Dict[str, Any]]:
        """
        搜索审计日志
        
        Args:
            action: 操作类型
            resource_type: 资源类型
            resource_id: 资源ID
            user_id: 用户ID
            username: 用户名
            status: 操作结果
            start_date: 开始日期
            end_date: 结束日期
            page: 页码，从1开始
            page_size: 每页数量
            
        Returns:
            Result[Dict[str, Any]]: 审计日志列表和分页信息
        """
        try:
            logs, total = await self.audit_log_repo.search(
                self.db,
                action=action,
                resource_type=resource_type,
                resource_id=resource_id,
                user_id=user_id,
                username=username,
                status=status,
                start_date=start_date,
                end_date=end_date,
                page=page,
                page_size=page_size
            )
            
            return ResultFactory.success({
                "items": [log.to_dict() for log in logs],
                "total": total,
                "page": page,
                "page_size": page_size
            })
        except Exception as e:
            self.logger.error(f"搜索审计日志失败: {str(e)}", exc_info=True)
            return ResultFactory.error(ErrorCode.INTERNAL_ERROR, f"搜索审计日志失败: {str(e)}")
    
    async def get_latest_audit_logs(self, limit: int = 10) -> Result[List[Dict[str, Any]]]:
        """
        获取最新的审计日志
        
        Args:
            limit: 返回数量限制
            
        Returns:
            Result[List[Dict[str, Any]]]: 审计日志列表
        """
        try:
            logs = await self.audit_log_repo.get_latest_logs(self.db, limit)
            return ResultFactory.success([log.to_dict() for log in logs])
        except Exception as e:
            self.logger.error(f"获取最新审计日志失败: {str(e)}", exc_info=True)
            return ResultFactory.error(ErrorCode.INTERNAL_ERROR, f"获取最新审计日志失败: {str(e)}")
    
    async def get_resource_audit_logs(
        self, 
        resource_type: str, 
        resource_id: str
    ) -> Result[List[Dict[str, Any]]]:
        """
        获取资源的审计日志
        
        Args:
            resource_type: 资源类型
            resource_id: 资源ID
            
        Returns:
            Result[List[Dict[str, Any]]]: 审计日志列表
        """
        try:
            logs = await self.audit_log_repo.get_actions_by_resource(
                self.db, resource_type, resource_id
            )
            return ResultFactory.success([log.to_dict() for log in logs])
        except Exception as e:
            self.logger.error(f"获取资源审计日志失败: {str(e)}", exc_info=True)
            return ResultFactory.error(
                ErrorCode.INTERNAL_ERROR, 
                f"获取资源审计日志失败: {str(e)}"
            ) 