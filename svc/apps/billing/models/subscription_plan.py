from typing import Optional, Dict, Any
from datetime import datetime

from sqlalchemy import Column, String, Integer, Boolean, DateTime, Float, BigInteger
from sqlalchemy.orm import relationship

from svc.core.database.session import Base
from svc.core.models.custom_types import DatabaseCompatibleJSON
from svc.core.utils.datetime_utils import get_utc_now_without_tzinfo


class SubscriptionPlan(Base):
    """订阅计划模型"""
    __tablename__ = "subscription_plans"
    
    id = Column(BigInteger, primary_key=True, autoincrement=True)
    user_id = Column(BigInteger, nullable=False, index=True)
    name = Column(String, nullable=False, index=True)
    description = Column(String, nullable=True)
    price = Column(Float, nullable=False)
    currency = Column(String, nullable=False, default="CNY")
    interval = Column(String, nullable=False, default="month")  # month, year
    interval_count = Column(Integer, nullable=False, default=1)
    trial_period_days = Column(Integer, nullable=True)
    is_active = Column(Boolean, default=True)
    features = Column(DatabaseCompatibleJSON, default=dict)
    meta_data = Column(DatabaseCompatibleJSON, default=dict)
    
    # 新增字段
    tier = Column(String, nullable=True, index=True)  # basic, premium, enterprise等层级
    valid_from = Column(DateTime, nullable=True)  # 计划生效日期
    valid_until = Column(DateTime, nullable=True)  # 计划失效日期
    max_users = Column(Integer, nullable=True)  # 最大用户数限制
    max_storage = Column(Integer, nullable=True)  # 最大存储空间限制(MB)
    max_projects = Column(Integer, nullable=True)  # 最大项目数
    sort_order = Column(Integer, nullable=True, default=0)  # 排序顺序
    
    created_at = Column(DateTime, default=get_utc_now_without_tzinfo)
    updated_at = Column(DateTime, default=get_utc_now_without_tzinfo, onupdate=get_utc_now_without_tzinfo)
    
    # 关系
    subscriptions = relationship("Subscription", back_populates="plan", cascade="all, delete-orphan") 