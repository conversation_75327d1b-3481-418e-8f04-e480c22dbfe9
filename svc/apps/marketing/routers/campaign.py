"""
营销活动API路由
包含活动的创建、查询、更新和删除功能
"""
from typing import Any, List, Optional, Dict

from fastapi import APIRouter, Depends, Query, Path, status

from svc.core.services.result import Result
from svc.apps.auth.dependencies import (
    get_current_active_user,
    has_permission,
    resource_permission
)
from svc.apps.auth.models.user import User
from svc.apps.marketing.schemas.campaign import (
    CampaignCreate,
    CampaignUpdate,
    CampaignListResponse,
    GetCampaignsParams
)
from svc.apps.marketing.services.campaign import CampaignService
from svc.apps.marketing.dependencies import get_campaign_service
from svc.core.exceptions import (
    handle_route_errors,
    CAMPAIGN_ERROR_MAPPING
)

# Re-introduce the file-level router, expected by the factory
router = APIRouter(
    tags=["营销活动"] # Tag for grouping in OpenAPI docs
)

# === 管理端路由 (Admin Routes) ===
# Paths prefixed with /admin within this router

@router.get("/admin/", response_model=Result[CampaignListResponse]) # Path: /campaigns/admin/
@handle_route_errors(CAMPAIGN_ERROR_MAPPING)
async def admin_list_campaigns(
    page_num: int = Query(1, ge=1, description="页码"),
    page_size: int = Query(10, ge=1, le=1000, description="每页数量"),
    status: Optional[str] = Query(None, description="活动状态"),
    campaign_service: CampaignService = Depends(get_campaign_service),
    current_user: User = Depends(get_current_active_user),
    _: bool = Depends(lambda: has_permission("campaign:read"))
) -> Result[CampaignListResponse]:
    """获取营销活动列表 (管理端)"""
    params = GetCampaignsParams(
        page_num=page_num,
        page_size=page_size,
        status=status
    )
    result = await campaign_service.get_campaigns(params=params)
    return result

@router.get("/admin/{campaign_id}", response_model=Result) # Path: /campaigns/admin/{campaign_id}
@handle_route_errors(CAMPAIGN_ERROR_MAPPING)
async def admin_get_campaign_details(
    campaign_id: int = Path(..., description="营销活动ID"),
    campaign_service: CampaignService = Depends(get_campaign_service),
    current_user: User = Depends(get_current_active_user),
    _: Any = Depends(resource_permission("campaign", "read")),
) -> Result:
    """获取营销活动详情 (管理端)"""
    result = await campaign_service.get_campaign(campaign_id=campaign_id)
    return result

@router.post("/admin/", response_model=Result, status_code=status.HTTP_201_CREATED) # Path: /campaigns/admin/
@handle_route_errors(CAMPAIGN_ERROR_MAPPING)
async def admin_create_campaign(
    campaign_data: CampaignCreate,
    campaign_service: CampaignService = Depends(get_campaign_service),
    current_user: User = Depends(get_current_active_user),
    _: bool = Depends(lambda: has_permission("campaign:create")),
) -> Result:
    """创建营销活动 (管理端)"""
    result = await campaign_service.create_campaign(params=campaign_data)
    return result

@router.put("/admin/{campaign_id}", response_model=Result) # Path: /campaigns/admin/{campaign_id}
@handle_route_errors(CAMPAIGN_ERROR_MAPPING)
async def admin_update_campaign(
    campaign_in: CampaignUpdate,
    campaign_id: int = Path(..., description="营销活动ID"),
    campaign_service: CampaignService = Depends(get_campaign_service),
    current_user: User = Depends(get_current_active_user),
    _: Any = Depends(resource_permission("campaign", "update")),
) -> Result:
    """更新营销活动 (管理端)"""
    result = await campaign_service.update_campaign(campaign_id=campaign_id, params=campaign_in)
    return result

@router.delete("/admin/{campaign_id}", response_model=Result) # Path: /campaigns/admin/{campaign_id}
@handle_route_errors(CAMPAIGN_ERROR_MAPPING)
async def admin_delete_campaign(
    campaign_id: int = Path(..., description="营销活动ID"),
    campaign_service: CampaignService = Depends(get_campaign_service),
    current_user: User = Depends(get_current_active_user),
    _: Any = Depends(resource_permission("campaign", "delete")),
) -> Result:
    """删除营销活动 (管理端)"""
    result = await campaign_service.delete_campaign(campaign_id=campaign_id)
    return result

@router.post("/admin/activate/{campaign_id}", response_model=Result) # Path: /campaigns/admin/activate/{campaign_id}
@handle_route_errors(CAMPAIGN_ERROR_MAPPING)
async def admin_activate_campaign(
    campaign_id: int = Path(..., description="营销活动ID"),
    campaign_service: CampaignService = Depends(get_campaign_service),
    current_user: User = Depends(get_current_active_user),
    _: Any = Depends(resource_permission("campaign", "update")),
) -> Result:
    """激活营销活动 (管理端)"""
    result = await campaign_service.activate_campaign(campaign_id=campaign_id)
    return result

@router.post("/admin/deactivate/{campaign_id}", response_model=Result) # Path: /campaigns/admin/deactivate/{campaign_id}
@handle_route_errors(CAMPAIGN_ERROR_MAPPING)
async def admin_deactivate_campaign(
    campaign_id: int = Path(..., description="营销活动ID"),
    campaign_service: CampaignService = Depends(get_campaign_service),
    current_user: User = Depends(get_current_active_user),
    _: Any = Depends(resource_permission("campaign", "update")),
) -> Result:
    """停用营销活动 (管理端)"""
    result = await campaign_service.deactivate_campaign(campaign_id=campaign_id)
    return result

@router.get("/admin/participants/{campaign_id}", response_model=Result) # Path: /campaigns/admin/participants/{campaign_id}
@handle_route_errors(CAMPAIGN_ERROR_MAPPING)
async def admin_get_campaign_participants(
    campaign_id: int = Path(..., description="营销活动ID"),
    page_num: int = Query(1, ge=1, description="页码"),
    page_size: int = Query(10, ge=1, le=1000, description="每页数量"),
    campaign_service: CampaignService = Depends(get_campaign_service),
    current_user: User = Depends(get_current_active_user),
    _: Any = Depends(resource_permission("campaign", "read")),
) -> Result:
    """获取营销活动参与者列表 (管理端)"""
    result = await campaign_service.get_campaign_participants(
        campaign_id=campaign_id, 
        page_num=page_num,
        page_size=page_size
    )
    return result

# === 客户端路由 (Client Routes) ===
# Paths are relative to the router prefix /campaigns

@router.get("/active", response_model=Result[CampaignListResponse]) # Path: /campaigns/active
@handle_route_errors(CAMPAIGN_ERROR_MAPPING)
async def list_active_campaigns(
    page_num: int = Query(1, ge=1, description="页码"),
    page_size: int = Query(10, ge=1, le=1000, description="每页数量"),
    campaign_service: CampaignService = Depends(get_campaign_service),
) -> Result[CampaignListResponse]:
    """获取所有活跃的营销活动 (客户端)"""
    result = await campaign_service.get_campaigns(params=GetCampaignsParams(status="active",page_num=page_num,page_size=page_size))
    return result

@router.get("/details/{campaign_id}", response_model=Result) # Path: /campaigns/details/{campaign_id}
@handle_route_errors(CAMPAIGN_ERROR_MAPPING)
async def get_campaign_details(
    campaign_id: int = Path(..., description="营销活动ID"),
    campaign_service: CampaignService = Depends(get_campaign_service),
    current_user: User = Depends(get_current_active_user),
) -> Result:
    """获取营销活动公开详情 (客户端)"""
    result = await campaign_service.get_campaign(campaign_id=campaign_id)
    return result

@router.get("/check-eligibility/{campaign_id}", response_model=Result) # Path: /campaigns/check-eligibility/{campaign_id}
@handle_route_errors(CAMPAIGN_ERROR_MAPPING)
async def check_campaign_eligibility(
    campaign_id: int = Path(..., description="活动ID"),
    campaign_service: CampaignService = Depends(get_campaign_service),
    current_user: User = Depends(get_current_active_user),
) -> Result:
    """检查用户参与活动资格 (客户端)"""
    result = await campaign_service.check_campaign_eligibility(campaign_id=campaign_id, user_id=current_user.id)
    return result 