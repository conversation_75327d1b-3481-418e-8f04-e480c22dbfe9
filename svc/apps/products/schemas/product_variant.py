from pydantic import BaseModel, Field, ConfigDict
from typing import List, Optional, Dict, Any
from datetime import datetime
from svc.core.schemas.base import PaginatedResponse

class ProductVariantBase(BaseModel):
    """商品变体基础模型"""
    model_config = ConfigDict(
        str_strip_whitespace=True,
        json_schema_extra={
            "example": {
                "product_id": 1,
                "name": "iPhone 15 Pro 钛原色 128GB",
                "sku": "IPHONE15PRO-TITANIUM-128GB",
                "barcode": "1234567890124",
                "price": 7999.00,
                "cost_price": 5000.00,
                "market_price": 8999.00,
                "stock_quantity": 50,
                "min_stock_level": 5,
                "weight": 0.187,
                "dimensions": {"length": 159.9, "width": 76.7, "height": 8.25},
                "attributes": {"color": "钛原色", "storage": "128GB"},
                "is_active": True,
                "is_default": True,
                "sort_order": 1
            }
        }
    )
    
    product_id: int = Field(..., description="商品ID")
    name: str = Field(..., description="变体名称")
    sku: str = Field(..., description="变体SKU")
    barcode: Optional[str] = Field(default=None, description="变体条码")
    price: Optional[float] = Field(default=None, description="变体价格")
    cost_price: Optional[float] = Field(default=None, description="变体成本价格")
    market_price: Optional[float] = Field(default=None, description="变体市场价格")
    stock_quantity: int = Field(default=0, description="变体库存数量")
    min_stock_level: int = Field(default=0, description="变体最低库存警戒线")
    weight: Optional[float] = Field(default=None, description="变体重量(kg)")
    dimensions: Dict[str, Any] = Field(default_factory=dict, description="变体尺寸信息")
    attributes: Dict[str, Any] = Field(default_factory=dict, description="变体属性")
    is_active: bool = Field(default=True, description="是否启用")
    is_default: bool = Field(default=False, description="是否为默认变体")
    sort_order: int = Field(default=0, description="排序顺序")

class ProductVariantCreate(ProductVariantBase):
    """商品变体创建模型"""
    pass

class ProductVariantUpdate(BaseModel):
    """商品变体更新模型"""
    model_config = ConfigDict(
        str_strip_whitespace=True,
        json_schema_extra={
            "example": {
                "name": "iPhone 15 Pro 钛蓝色 256GB",
                "price": 8999.00,
                "stock_quantity": 30,
                "is_active": True
            }
        }
    )
    
    name: Optional[str] = Field(default=None, description="变体名称")
    sku: Optional[str] = Field(default=None, description="变体SKU")
    barcode: Optional[str] = Field(default=None, description="变体条码")
    price: Optional[float] = Field(default=None, description="变体价格")
    cost_price: Optional[float] = Field(default=None, description="变体成本价格")
    market_price: Optional[float] = Field(default=None, description="变体市场价格")
    stock_quantity: Optional[int] = Field(default=None, description="变体库存数量")
    min_stock_level: Optional[int] = Field(default=None, description="变体最低库存警戒线")
    weight: Optional[float] = Field(default=None, description="变体重量(kg)")
    dimensions: Optional[Dict[str, Any]] = Field(default=None, description="变体尺寸信息")
    attributes: Optional[Dict[str, Any]] = Field(default=None, description="变体属性")
    is_active: Optional[bool] = Field(default=None, description="是否启用")
    is_default: Optional[bool] = Field(default=None, description="是否为默认变体")
    sort_order: Optional[int] = Field(default=None, description="排序顺序")

class ProductVariantResponse(ProductVariantBase):
    """商品变体响应模型"""
    model_config = ConfigDict(
        from_attributes=True,
        str_strip_whitespace=True,
        json_schema_extra={
            "example": {
                "id": 1,
                "created_at": "2024-03-24T12:00:00",
                "updated_at": "2024-03-24T12:00:00"
            }
        }
    )
    
    id: int = Field(description="变体ID")
    created_at: datetime = Field(description="创建时间")
    updated_at: datetime = Field(description="更新时间")

class ProductVariantListResponse(PaginatedResponse[ProductVariantResponse]):
    """商品变体列表响应模型"""
    pass
