"""
系统模块的依赖注入函数

本模块提供系统管理相关的依赖注入函数，用于在路由和服务间共享实例。
"""

from functools import lru_cache
from typing import Any, Dict, Optional

from fastapi import Depends
from redis.asyncio import Redis
from sqlalchemy.ext.asyncio import AsyncSession

from svc.apps.system.repositories.audit_log import AuditLogRepository
from svc.apps.system.services.audit_log import AuditLogService
from svc.apps.system.services.monitor import SystemMonitorService
from svc.apps.system.services.system import SystemService
from svc.core.cache.redis import get_redis
from svc.core.database import get_session_for_route
from svc.core.dependencies.auth import configure_auth_dependencies


# 设置系统依赖项，注册资源类型
def setup_system_dependencies():
    """
    设置系统依赖项
    
    注册系统相关的资源类型。
    应在应用启动时调用此函数。
    """
    try:
        # 导入需要注册的模型
        from svc.apps.system.models import AuditLog

        # 注册系统模块资源类型
        configure_auth_dependencies(
            resources={
                "audit_log": AuditLog
            }
        )
    except ImportError:
        # 如果模型未定义或导入失败，记录日志但不中断程序
        import logging
        logger = logging.getLogger(__name__)
        logger.warning("未能注册系统模块资源类型，请检查模型是否正确定义")


# 服务实例配置字典
@lru_cache
def get_service_config() -> Dict[str, Any]:
    """获取服务配置字典
    
    返回一个配置字典，用于缓存服务实例
    
    Returns:
        Dict[str, Any]: 配置字典
    """
    return {
        "system_service": None,  # 由get_system_service初始化
        "audit_log_service": None,  # 由get_audit_log_service初始化
        "monitor_service": None  # 由get_monitor_service初始化
    }

# 全局配置字典
config = get_service_config()


# 仓库依赖项
async def get_audit_log_repository() -> AuditLogRepository:
    """获取审计日志仓库实例

    Returns:
        AuditLogRepository: 审计日志仓库实例
    """
    return AuditLogRepository()


# 服务依赖项
async def get_system_service(
    db: AsyncSession = Depends(get_session_for_route()),
    redis: Optional[Redis] = Depends(get_redis),
    audit_log_repo: AuditLogRepository = Depends(get_audit_log_repository)
) -> SystemService:
    """获取系统服务实例

    Args:
        db: 数据库会话
        redis: Redis客户端
        audit_log_repo: 审计日志仓库实例

    Returns:
        SystemService: 系统服务实例
    """
    if config["system_service"] is None:
        # 创建审计日志服务和监控服务
        audit_log_service = AuditLogService(db, redis, audit_log_repo)
        monitor_service = SystemMonitorService(db, redis)

        config["system_service"] = SystemService(
            db=db,
            redis=redis,
            audit_log_service=audit_log_service,
            monitor_service=monitor_service
        )
    else:
        # 更新数据库会话和Redis客户端
        config["system_service"].db = db
        config["system_service"].redis = redis
        # 更新子服务的数据库会话
        config["system_service"].audit_log_service.db = db
        config["system_service"].audit_log_service.redis = redis
        config["system_service"].monitor_service.db = db
        config["system_service"].monitor_service.redis = redis

    return config["system_service"]


async def get_monitor_service(
    db: AsyncSession = Depends(get_session_for_route()),
    redis: Optional[Redis] = Depends(get_redis)
) -> SystemMonitorService:
    """获取系统监控服务实例

    Args:
        db: 数据库会话
        redis: Redis客户端

    Returns:
        SystemMonitorService: 系统监控服务实例
    """
    if config["monitor_service"] is None:
        config["monitor_service"] = SystemMonitorService(db=db, redis=redis)
    else:
        # 更新数据库会话和Redis客户端
        config["monitor_service"].db = db
        config["monitor_service"].redis = redis

    return config["monitor_service"]


async def get_audit_log_service(
    db: AsyncSession = Depends(get_session_for_route()),
    redis: Optional[Redis] = Depends(get_redis),
    audit_log_repo: AuditLogRepository = Depends(get_audit_log_repository)
) -> AuditLogService:
    """获取审计日志服务实例
    
    Args:
        db: 数据库会话
        redis: Redis客户端
        audit_log_repo: 审计日志仓库实例
        
    Returns:
        AuditLogService: 审计日志服务实例
    """
    if config["audit_log_service"] is None:
        config["audit_log_service"] = AuditLogService(
            db=db,
            redis=redis,
            audit_log_repo=audit_log_repo
        )
    else:
        # 更新数据库会话和Redis客户端
        config["audit_log_service"].db = db
        config["audit_log_service"].redis = redis
        config["audit_log_service"].audit_log_repo = audit_log_repo
    
    return config["audit_log_service"]

# 在模块导入时自动调用设置函数
setup_system_dependencies()
