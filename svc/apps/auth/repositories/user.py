"""
用户数据访问层。
负责用户模型的数据库访问操作，实现数据访问与业务逻辑分离。
"""

from typing import Optional, List, Dict, Any, Tuple, Set
from sqlalchemy.ext.asyncio import AsyncSession
from sqlalchemy.future import select
from sqlalchemy import or_, desc, asc, and_
from sqlalchemy.sql import func

from svc.apps.auth.models.user import User
from svc.apps.auth.models.role import Role
from svc.apps.auth.models.user_role import user_role
from svc.apps.auth.schemas.user import UserCreate, UserUpdate
from svc.core.repositories.base import BaseRepository
from svc.core.security.password import get_password_hash
from svc.core.utils.datetime_utils import get_utc_now_without_tzinfo


class UserRepository(BaseRepository[User, UserCreate, UserUpdate]):
    """用户仓库类，提供用户数据访问方法"""
    
    def __init__(self,db: AsyncSession):
        """初始化用户仓库"""
        super().__init__(model=User,db=db)
    
    async def get_by_email(self, email: str) -> Optional[User]:
        """
        通过邮箱获取用户
        
        Args:
            db: 数据库会话
            email: 用户邮箱
            
        Returns:
            Optional[User]: 用户对象，如果不存在则返回None
        """
        return await self.get_one(email=email)
    
    async def get_by_username(self, username: str) -> Optional[User]:
        """
        通过用户名获取用户
        
        Args:
            db: 数据库会话
            username: 用户名
            
        Returns:
            Optional[User]: 用户对象，如果不存在则返回None
        """
        return await self.get_one(username=username)
    
    # get_by_id 直接使用父类BaseRepository.get_by_id方法
    
    async def create(
        self,
       
        *,
        email: str,
        password: str,
        fullname: Optional[str] = None,
        is_superuser: bool = False,
        username: Optional[str] = None,
    ) -> User:
        """
        创建新用户
        
        Args:
            db: 数据库会话
            email: 用户邮箱
            password: 用户密码（明文，将被哈希处理）
            fullname: 用户全名
            is_superuser: 是否为超级管理员
            username: 用户名，如不提供则使用邮箱前缀
            
        Returns:
            User: 创建的用户对象
        """
        hashed_password = get_password_hash(password)
        data = {
            "email": email,
            "hashed_password": hashed_password,
            "username": username or email.split('@')[0],
            "fullname": fullname,
            "is_superuser": is_superuser,
        }
        return await super().create(data)
    
    async def update(
        self,
       
        user: User,
        *,
        email: Optional[str] = None,
        fullname: Optional[str] = None,
        password: Optional[str] = None,
        is_active: Optional[bool] = None,
        is_superuser: Optional[bool] = None,
        preferences: Optional[dict] = None,
        username: Optional[str] = None,
    ) -> User:
        """
        更新用户信息
        
        Args:
            db: 数据库会话
            user: 要更新的用户对象
            email: 新邮箱
            fullname: 新全名
            password: 新密码（明文，将被哈希处理）
            is_active: 是否激活
            is_superuser: 是否为超级管理员
            preferences: 用户偏好设置
            username: 新用户名
            
        Returns:
            User: 更新后的用户对象
        """
        data = {}
        if email is not None:
            data["email"] = email
        if fullname is not None:
            data["fullname"] = fullname
        if password is not None:
            data["hashed_password"] = get_password_hash(password)
        if is_active is not None:
            data["is_active"] = is_active
        if is_superuser is not None:
            data["is_superuser"] = is_superuser
        if username is not None:
            data["username"] = username
        
        if data:
            return await super().update(user, data)
        return user
    
    # delete方法可以直接使用BaseRepository.delete
    
    async def get_users(
        self,
       
        *,
        skip: int = 0,
        limit: int = 100,
        is_active: Optional[bool] = None,
        search_term: Optional[str] = None,
        order_by: str = "id",
        order_desc: bool = False
    ) -> Tuple[List[User], int]:
        """
        获取用户列表和总数
        
        Args:
            db: 数据库会话
            skip: 跳过记录数
            limit: 限制返回记录数
            is_active: 是否筛选活跃用户
            search_term: 搜索关键词
            order_by: 排序字段
            order_desc: 是否降序排序
            
        Returns:
            Tuple[List[User], int]: 用户列表和总数
        """
        if not search_term:
            # 如果没有搜索关键词，可以使用父类的get_paginated方法
            filters = {}
            if is_active is not None:
                filters["is_active"] = is_active
                
            page = skip // limit + 1
            return await self.get_paginated(
                page=page,
                page_size=limit,
                order_by=order_by,
                order_direction="desc" if order_desc else "asc",
                **filters
            )
        
        # 有搜索关键词时，需要使用特定的搜索逻辑
        query = select(User)
        
        # 应用过滤条件
        if is_active is not None:
            query = query.filter(User.is_active == is_active)
            
        if search_term:
            search_pattern = f"%{search_term}%"
            query = query.filter(
                or_(
                    User.username.ilike(search_pattern),
                    User.email.ilike(search_pattern),
                    User.fullname.ilike(search_pattern)
                )
            )
        
        # 计算总数
        count_query = select(func.count()).select_from(query.subquery())
        total = await self.db.scalar(count_query)
        
        # 排序
        order_column = getattr(User, order_by, User.id)
        if order_desc:
            query = query.order_by(desc(order_column))
        else:
            query = query.order_by(asc(order_column))
            
        # 分页
        query = query.offset(skip).limit(limit)
        
        # 执行查询
        result = await self.db.execute(query)
        users = result.scalars().all()
        
        return users, total
    
    async def add_role(self, user: User, role: Role) -> None:
        """
        为用户添加角色
        
        Args:
            db: 数据库会话
            user: 用户对象
            role: 角色对象
        """
        # 检查角色是否已存在
        exists_query = select(user_role).where(
            and_(
                user_role.c.user_id == user.id,
                user_role.c.role_id == role.id
            )
        )
        result = await self.db.execute(exists_query)
        if result.first() is None:
            # 角色不存在，添加关联
            stmt = user_role.insert().values(user_id=user.id, role_id=role.id)
            await self.db.execute(stmt)
            await self.db.commit()
    
    async def remove_role(self, user: User, role: Role) -> None:
        """
        移除用户角色
        
        Args:
            db: 数据库会话
            user: 用户对象
            role: 角色对象
        """
        stmt = user_role.delete().where(
            and_(
                user_role.c.user_id == user.id,
                user_role.c.role_id == role.id
            )
        )
        await self.db.execute(stmt)
        await self.db.commit()
    
    async def get_roles(self, user: User) -> List[Role]:
        """
        获取用户角色列表
        
        Args:
            db: 数据库会话
            user: 用户对象
            
        Returns:
            List[Role]: 用户角色列表
        """
        query = (
            select(Role)
            .join(user_role, Role.id == user_role.c.role_id)
            .where(user_role.c.user_id == user.id)
        )
        result = await self.db.execute(query)
        return result.scalars().all()
    
    async def save(self, user: User) -> User:
        """
        保存用户对象
        
        Args:
            db: 数据库会话
            user: 用户对象
            
        Returns:
            User: 保存后的用户对象
        """
        self.db.add(user)
        await self.db.commit()
        await self.db.refresh(user)
        return user
    
    async def update_last_login(self, user: User) -> User:
        """
        更新用户最后登录时间
        
        Args:
            db: 数据库会话
            user: 用户对象
            
        Returns:
            User: 更新后的用户对象
        """
        # 这里需要保留，因为这是特定的业务逻辑
        # 实现更新最后登录时间的逻辑
        user.last_login = get_utc_now_without_tzinfo()
        await self.db.commit()
        return user
    
    async def get_user_with_roles(
        self,
       
        user_id: int,
        skip: int = 0,
        limit: int = 20
    ) -> Tuple[Optional[User], List[Role]]:
        """
        获取用户及其角色
        
        Args:
            db: 数据库会话
            user_id: 用户ID
            skip: 跳过记录数
            limit: 限制返回记录数
            
        Returns:
            Tuple[Optional[User], List[Role]]: 用户对象和角色列表
        """
        # 获取用户
        user = await self.get_by_id(user_id)
        if not user:
            return None, []
            
        # 获取用户角色
        roles = await self.get_roles(user)
        return user, roles
    
    async def has_permission(self, user: User, permission: str) -> bool:
        """
        检查用户是否有指定权限
        
        Args:
            db: 数据库会话
            user: 用户对象
            permission: 权限名称
            
        Returns:
            bool: 用户是否拥有该权限
        """
        if user.is_superuser:
            return True
            
        roles = await self.get_roles(user)
        return any(role.has_permission(permission) for role in roles)
    
    async def has_role(self, user: User, role_name: str) -> bool:
        """
        检查用户是否有指定角色
        
        Args:
            db: 数据库会话
            user: 用户对象
            role_name: 角色名称
            
        Returns:
            bool: 用户是否拥有该角色
        """
        roles = await self.get_roles(user)
        return any(role.name == role_name for role in roles)
    
    async def get_user_permissions(self, user: User) -> Set[str]:
        """
        获取用户的所有权限
        
        Args:
            db: 数据库会话
            user: 用户对象
            
        Returns:
            Set[str]: 用户的权限集合
        """
        if user.is_superuser:
            # 超级管理员拥有所有权限
            return {"*:*"}  # 通配符表示所有权限
            
        roles = await self.get_roles(user)
        permissions = set()
        for role in roles:
            permissions.update(perm.name for perm in role.permissions)
        return permissions
    
    async def to_dict_with_roles(self, user: User) -> Dict[str, Any]:
        """
        将用户对象转换为包含角色信息的字典
        
        Args:
            db: 数据库会话
            user: 用户对象
            
        Returns:
            Dict[str, Any]: 包含角色信息的用户字典
        """
        roles = await self.get_roles(user)
        role_names = [role.name for role in roles]
        return user.to_dict(role_names=role_names) 