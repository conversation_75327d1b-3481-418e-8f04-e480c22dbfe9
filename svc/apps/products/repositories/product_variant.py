"""
商品变体数据访问层。
负责商品变体模型的数据库访问操作，实现数据访问与业务逻辑分离。
"""

from typing import Optional, List, Tuple
from sqlalchemy.ext.asyncio import AsyncSession
from sqlalchemy.future import select
from sqlalchemy import and_, desc, asc, func

from svc.core.repositories import BaseRepository
from svc.apps.products.models.product_variant import ProductVariant
from svc.apps.products.schemas.product_variant import ProductVariantCreate, ProductVariantUpdate

class ProductVariantRepository(BaseRepository[ProductVariant, ProductVariantCreate, ProductVariantUpdate]):
    """商品变体仓库类，提供商品变体数据访问方法
    
    该仓库类实现了ProductVariant模型的数据访问操作，
    包括变体的基本CRUD操作以及特定的数据查询和统计功能。
    """
    
    def __init__(self, db: AsyncSession):
        """初始化商品变体仓库"""
        super().__init__(db, ProductVariant)
    
    async def get_by_sku(self, sku: str) -> Optional[ProductVariant]:
        """通过SKU获取商品变体
        
        Args:
            sku: 变体SKU
            
        Returns:
            Optional[ProductVariant]: 变体对象，不存在则返回None
        """
        return await self.get_one(sku=sku)
    
    async def get_by_barcode(self, barcode: str) -> Optional[ProductVariant]:
        """通过条码获取商品变体
        
        Args:
            barcode: 变体条码
            
        Returns:
            Optional[ProductVariant]: 变体对象，不存在则返回None
        """
        return await self.get_one(barcode=barcode)
    
    async def get_by_product_id(
        self, 
        product_id: int,
        skip: int = 0,
        limit: int = 100,
        order_by: str = "sort_order",
        order_desc: bool = False
    ) -> Tuple[List[ProductVariant], int]:
        """获取指定商品的所有变体
        
        Args:
            product_id: 商品ID
            skip: 跳过记录数
            limit: 返回记录数
            order_by: 排序字段
            order_desc: 是否降序
            
        Returns:
            Tuple[List[ProductVariant], int]: 变体列表和总数
        """
        # Get paginated list
        query = select(self.model).where(
            self.model.product_id == product_id
        )
        
        # 添加排序
        if order_by and hasattr(self.model, order_by):
            order_column = getattr(self.model, order_by)
            if order_desc:
                query = query.order_by(desc(order_column))
            else:
                query = query.order_by(asc(order_column))
        
        query = query.offset(skip).limit(limit)
        result = await self.db.execute(query)
        variants = result.scalars().all()
        
        # Get total count
        count_query = select(func.count()).select_from(self.model).where(
            self.model.product_id == product_id
        )
        count_result = await self.db.execute(count_query)
        total = count_result.scalar() or 0
        
        return variants, total
    
    async def get_active_variants_by_product_id(
        self, 
        product_id: int,
        skip: int = 0,
        limit: int = 100
    ) -> Tuple[List[ProductVariant], int]:
        """获取指定商品的所有启用变体
        
        Args:
            product_id: 商品ID
            skip: 跳过记录数
            limit: 返回记录数
            
        Returns:
            Tuple[List[ProductVariant], int]: 变体列表和总数
        """
        conditions = [
            self.model.product_id == product_id,
            self.model.is_active == True
        ]
        
        # Get paginated list
        query = select(self.model).where(
            *conditions
        ).order_by(asc(self.model.sort_order)).offset(skip).limit(limit)
        result = await self.db.execute(query)
        variants = result.scalars().all()
        
        # Get total count
        count_query = select(func.count()).select_from(self.model).where(*conditions)
        count_result = await self.db.execute(count_query)
        total = count_result.scalar() or 0
        
        return variants, total
    
    async def get_default_variant_by_product_id(self, product_id: int) -> Optional[ProductVariant]:
        """获取指定商品的默认变体
        
        Args:
            product_id: 商品ID
            
        Returns:
            Optional[ProductVariant]: 默认变体对象，不存在则返回None
        """
        return await self.get_one(product_id=product_id, is_default=True)
    
    async def get_low_stock_variants(
        self, 
        limit: int = 10, 
        skip: int = 0
    ) -> Tuple[List[ProductVariant], int]:
        """获取库存不足的变体
        
        Args:
            limit: 返回结果数量限制
            skip: 结果偏移量
            
        Returns:
            Tuple[List[ProductVariant], int]: 变体列表和总数
        """
        conditions = [
            self.model.is_active == True,
            self.model.stock_quantity <= self.model.min_stock_level
        ]
        
        # Get paginated list
        query = select(self.model).where(
            *conditions
        ).order_by(asc(self.model.stock_quantity)).offset(skip).limit(limit)
        result = await self.db.execute(query)
        variants = result.scalars().all()
        
        # Get total count
        count_query = select(func.count()).select_from(self.model).where(*conditions)
        count_result = await self.db.execute(count_query)
        total = count_result.scalar() or 0
        
        return variants, total
    
    async def update_stock_quantity(
        self, 
        variant_id: int, 
        quantity_change: int
    ) -> Optional[ProductVariant]:
        """更新变体库存数量
        
        Args:
            variant_id: 变体ID
            quantity_change: 库存变化量（正数为增加，负数为减少）
            
        Returns:
            Optional[ProductVariant]: 更新后的变体对象，不存在则返回None
        """
        variant = await self.get_by_id(variant_id)
        if not variant:
            return None
            
        new_quantity = max(0, variant.stock_quantity + quantity_change)
        update_data = {"stock_quantity": new_quantity}
        return await self.update(variant, update_data)
    
    async def set_default_variant(
        self, 
        product_id: int, 
        variant_id: int
    ) -> Optional[ProductVariant]:
        """设置商品的默认变体
        
        Args:
            product_id: 商品ID
            variant_id: 要设置为默认的变体ID
            
        Returns:
            Optional[ProductVariant]: 设置后的默认变体对象，不存在则返回None
        """
        # 首先将该商品的所有变体的is_default设为False
        query = select(self.model).where(self.model.product_id == product_id)
        result = await self.db.execute(query)
        variants = result.scalars().all()
        
        for variant in variants:
            if variant.is_default:
                await self.update(variant, {"is_default": False})
        
        # 然后将指定变体设为默认
        target_variant = await self.get_by_id(variant_id)
        if target_variant and target_variant.product_id == product_id:
            return await self.update(target_variant, {"is_default": True})
        
        return None
    
    async def get_variants_by_attributes(
        self, 
        product_id: int, 
        attributes: dict
    ) -> List[ProductVariant]:
        """根据属性查找变体
        
        Args:
            product_id: 商品ID
            attributes: 属性字典
            
        Returns:
            List[ProductVariant]: 匹配的变体列表
        """
        # 这里简化实现，实际可能需要更复杂的JSON查询
        query = select(self.model).where(
            self.model.product_id == product_id,
            self.model.is_active == True
        )
        result = await self.db.execute(query)
        all_variants = result.scalars().all()
        
        # 在Python中过滤匹配的变体
        matching_variants = []
        for variant in all_variants:
            if all(
                variant.attributes.get(key) == value 
                for key, value in attributes.items()
            ):
                matching_variants.append(variant)
        
        return matching_variants
