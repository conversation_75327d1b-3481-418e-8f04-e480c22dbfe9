"""
产品验证器工具类
提供商品数据验证功能，包括数据完整性、业务规则、格式验证等
"""

import logging
import re
from typing import Any, Dict, List, Optional, Union

from svc.apps.products.models.product import ProductStatus


class ValidationError(Exception):
    """验证错误异常"""
    pass


class ProductValidator:
    """
    产品验证器类，提供各种产品数据验证功能
    
    主要功能：
    - 基础数据验证
    - 业务规则验证
    - SKU格式验证
    - 价格合理性验证
    - 库存一致性验证
    - 产品完整性验证
    """
    
    def __init__(self):
        """初始化产品验证器"""
        self.logger = logging.getLogger(__name__)
        
        # SKU格式规则
        self.sku_pattern = re.compile(r'^[A-Z0-9]{3,20}$')
        
        # 条码格式规则（支持EAN-13, UPC-A等）
        self.barcode_patterns = {
            'ean13': re.compile(r'^\d{13}$'),
            'ean8': re.compile(r'^\d{8}$'),
            'upc_a': re.compile(r'^\d{12}$'),
            'code128': re.compile(r'^[A-Za-z0-9\-\.\s]{1,48}$')
        }
    
    def validate_product(
        self,
        product_data: Dict[str, Any],
        strict_mode: bool = False
    ) -> Dict[str, Any]:
        """
        验证产品数据
        
        Args:
            product_data: 产品数据字典
            strict_mode: 严格模式，启用更严格的验证规则
            
        Returns:
            Dict: 验证结果
        """
        errors = []
        warnings = []
        
        try:
            # 基础字段验证
            basic_validation = self._validate_basic_fields(product_data)
            errors.extend(basic_validation.get('errors', []))
            warnings.extend(basic_validation.get('warnings', []))
            
            # SKU验证
            sku_validation = self._validate_sku(product_data.get('sku'))
            if not sku_validation['valid']:
                errors.extend(sku_validation['errors'])
            
            # 价格验证
            price_validation = self._validate_prices(product_data)
            errors.extend(price_validation.get('errors', []))
            warnings.extend(price_validation.get('warnings', []))
            
            # 库存验证
            inventory_validation = self._validate_inventory_data(product_data)
            errors.extend(inventory_validation.get('errors', []))
            warnings.extend(inventory_validation.get('warnings', []))
            
            # 条码验证
            if product_data.get('barcode'):
                barcode_validation = self._validate_barcode(product_data['barcode'])
                if not barcode_validation['valid']:
                    if strict_mode:
                        errors.extend(barcode_validation['errors'])
                    else:
                        warnings.extend(barcode_validation['errors'])
            
            # 业务规则验证
            if strict_mode:
                business_validation = self._validate_business_rules(product_data)
                errors.extend(business_validation.get('errors', []))
                warnings.extend(business_validation.get('warnings', []))
            
            return {
                'valid': len(errors) == 0,
                'errors': errors,
                'warnings': warnings,
                'error_count': len(errors),
                'warning_count': len(warnings)
            }
        except Exception as e:
            self.logger.error(f"产品验证失败: {str(e)}")
            return {
                'valid': False,
                'errors': [f'验证过程出错: {str(e)}'],
                'warnings': [],
                'error_count': 1,
                'warning_count': 0
            }
    
    def validate_product_variant(
        self,
        variant_data: Dict[str, Any],
        parent_product: Optional[Dict[str, Any]] = None
    ) -> Dict[str, Any]:
        """
        验证产品变体数据
        
        Args:
            variant_data: 变体数据字典
            parent_product: 父产品数据
            
        Returns:
            Dict: 验证结果
        """
        errors = []
        warnings = []
        
        try:
            # 基础字段验证
            required_fields = ['name', 'sku', 'product_id']
            for field in required_fields:
                if not variant_data.get(field):
                    errors.append(f'变体缺少必填字段: {field}')
            
            # SKU验证
            sku_validation = self._validate_sku(variant_data.get('sku'))
            if not sku_validation['valid']:
                errors.extend(sku_validation['errors'])
            
            # 价格验证（变体价格可以为空，使用父产品价格）
            if variant_data.get('price') is not None:
                price_validation = self._validate_prices(variant_data)
                errors.extend(price_validation.get('errors', []))
            
            # 与父产品的一致性验证
            if parent_product:
                consistency_validation = self._validate_variant_consistency(variant_data, parent_product)
                warnings.extend(consistency_validation.get('warnings', []))
            
            return {
                'valid': len(errors) == 0,
                'errors': errors,
                'warnings': warnings,
                'error_count': len(errors),
                'warning_count': len(warnings)
            }
        except Exception as e:
            self.logger.error(f"变体验证失败: {str(e)}")
            return {
                'valid': False,
                'errors': [f'验证过程出错: {str(e)}'],
                'warnings': [],
                'error_count': 1,
                'warning_count': 0
            }
    
    def validate_inventory_consistency(
        self,
        product: Union[Dict[str, Any], Any],
        inventories: List[Union[Dict[str, Any], Any]]
    ) -> Dict[str, Any]:
        """
        验证库存一致性
        
        Args:
            product: 产品对象或数据
            inventories: 库存记录列表
            
        Returns:
            Dict: 验证结果
        """
        errors = []
        warnings = []
        
        try:
            # 获取产品库存数量
            if isinstance(product, dict):
                product_stock = product.get('stock_quantity', 0)
                track_inventory = product.get('track_inventory', True)
            else:
                product_stock = product.stock_quantity
                track_inventory = product.track_inventory
            
            # 计算库存记录总量
            total_inventory = 0
            available_inventory = 0
            
            for inventory in inventories:
                if isinstance(inventory, dict):
                    total_inventory += inventory.get('quantity', 0)
                    available_inventory += inventory.get('available_quantity', 0)
                else:
                    total_inventory += inventory.quantity
                    available_inventory += inventory.available_quantity
            
            # 验证库存一致性
            if track_inventory:
                if abs(product_stock - total_inventory) > 0:
                    errors.append(f'产品库存({product_stock})与库存记录总量({total_inventory})不一致')
                
                if product_stock > 0 and available_inventory == 0:
                    warnings.append('产品显示有库存但没有可用库存记录')
            
            return {
                'valid': len(errors) == 0,
                'errors': errors,
                'warnings': warnings,
                'product_stock': product_stock,
                'total_inventory': total_inventory,
                'available_inventory': available_inventory
            }
        except Exception as e:
            self.logger.error(f"库存一致性验证失败: {str(e)}")
            return {
                'valid': False,
                'errors': [f'验证过程出错: {str(e)}'],
                'warnings': [],
                'product_stock': 0,
                'total_inventory': 0,
                'available_inventory': 0
            }
    
    def _validate_basic_fields(self, data: Dict[str, Any]) -> Dict[str, Any]:
        """验证基础字段"""
        errors = []
        warnings = []
        
        # 必填字段
        required_fields = ['name', 'sku', 'price']
        for field in required_fields:
            if not data.get(field):
                errors.append(f'缺少必填字段: {field}')
        
        # 字段长度验证
        if data.get('name') and len(data['name']) > 200:
            errors.append('商品名称长度不能超过200字符')
        
        if data.get('short_description') and len(data['short_description']) > 500:
            errors.append('简短描述长度不能超过500字符')
        
        # 状态验证
        if data.get('status') and data['status'] not in [s.value for s in ProductStatus]:
            errors.append(f'无效的商品状态: {data["status"]}')
        
        return {'errors': errors, 'warnings': warnings}
    
    def _validate_sku(self, sku: str) -> Dict[str, Any]:
        """验证SKU格式"""
        if not sku:
            return {'valid': False, 'errors': ['SKU不能为空']}
        
        if not self.sku_pattern.match(sku):
            return {
                'valid': False,
                'errors': ['SKU格式无效，应为3-20位大写字母和数字组合']
            }
        
        return {'valid': True, 'errors': []}
    
    def _validate_barcode(self, barcode: str) -> Dict[str, Any]:
        """验证条码格式"""
        if not barcode:
            return {'valid': True, 'errors': []}
        
        for barcode_type, pattern in self.barcode_patterns.items():
            if pattern.match(barcode):
                return {'valid': True, 'errors': [], 'type': barcode_type}
        
        return {
            'valid': False,
            'errors': ['条码格式无效，支持EAN-13、EAN-8、UPC-A、Code128格式']
        }
    
    def _validate_prices(self, data: Dict[str, Any]) -> Dict[str, Any]:
        """验证价格数据"""
        errors = []
        warnings = []
        
        price = data.get('price')
        cost_price = data.get('cost_price')
        market_price = data.get('market_price')
        
        # 价格必须为正数
        if price is not None and price < 0:
            errors.append('销售价格不能为负数')
        
        if cost_price is not None and cost_price < 0:
            errors.append('成本价格不能为负数')
        
        if market_price is not None and market_price < 0:
            errors.append('市场价格不能为负数')
        
        # 价格合理性检查
        if price is not None and cost_price is not None:
            if price < cost_price:
                warnings.append('销售价格低于成本价格，可能导致亏损')
        
        if price is not None and market_price is not None:
            if price > market_price * 1.5:
                warnings.append('销售价格显著高于市场价格')
        
        return {'errors': errors, 'warnings': warnings}
    
    def _validate_inventory_data(self, data: Dict[str, Any]) -> Dict[str, Any]:
        """验证库存数据"""
        errors = []
        warnings = []
        
        stock_quantity = data.get('stock_quantity')
        min_stock_level = data.get('min_stock_level')
        max_stock_level = data.get('max_stock_level')
        track_inventory = data.get('track_inventory', True)
        
        # 库存数量验证
        if stock_quantity is not None and stock_quantity < 0:
            errors.append('库存数量不能为负数')
        
        if min_stock_level is not None and min_stock_level < 0:
            errors.append('最低库存不能为负数')
        
        if max_stock_level is not None and max_stock_level < 0:
            errors.append('最高库存不能为负数')
        
        # 库存逻辑验证
        if (min_stock_level is not None and max_stock_level is not None and 
            min_stock_level > max_stock_level):
            errors.append('最低库存不能大于最高库存')
        
        if (track_inventory and stock_quantity is not None and 
            min_stock_level is not None and stock_quantity < min_stock_level):
            warnings.append('当前库存低于最低库存警戒线')
        
        return {'errors': errors, 'warnings': warnings}
    
    def _validate_business_rules(self, data: Dict[str, Any]) -> Dict[str, Any]:
        """验证业务规则"""
        errors = []
        warnings = []
        
        # 数字商品不应该跟踪库存
        if data.get('is_digital') and data.get('track_inventory'):
            warnings.append('数字商品通常不需要跟踪库存')
        
        # 推荐商品应该有库存
        if (data.get('is_featured') and data.get('track_inventory') and 
            data.get('stock_quantity', 0) <= 0):
            warnings.append('推荐商品应该保持有库存状态')
        
        # 已发布商品应该有完整信息
        if data.get('status') == ProductStatus.ACTIVE:
            if not data.get('description'):
                warnings.append('已发布商品建议添加详细描述')
            
            if not data.get('category_id'):
                warnings.append('已发布商品建议设置分类')
        
        return {'errors': errors, 'warnings': warnings}
    
    def _validate_variant_consistency(
        self,
        variant_data: Dict[str, Any],
        parent_product: Dict[str, Any]
    ) -> Dict[str, Any]:
        """验证变体与父产品的一致性"""
        warnings = []
        
        # 货币单位应该一致
        if (variant_data.get('price') is not None and 
            parent_product.get('currency') and
            variant_data.get('currency') != parent_product['currency']):
            warnings.append('变体货币单位与父产品不一致')
        
        # 数字商品的变体也应该是数字商品
        if (parent_product.get('is_digital') and 
            not variant_data.get('is_digital')):
            warnings.append('数字商品的变体也应该标记为数字商品')
        
        return {'warnings': warnings}
