"""
数据库种子数据脚本。
用于初始化数据库的基础数据。
"""
import asyncio
import json
import logging
import sys
import unittest.mock
from datetime import datetime, timedelta, timezone
from typing import List, Optional

# 模拟事件总线，避免"no running event loop"错误
with unittest.mock.patch('asyncio.create_task'):
    from sqlalchemy import insert
    from sqlalchemy.ext.asyncio import AsyncSession
    from sqlalchemy.future import select

    from svc.apps.auth.models import Permission, Role, User, user_role
    from svc.apps.auth.models.wechat_user import WechatUser
    from svc.apps.auth.repositories import RoleRepository, UserRepository
    from svc.apps.billing.models.subscription_plan import SubscriptionPlan
    from svc.apps.billing.repositories import SubscriptionPlanRepository
    from svc.apps.marketing.models.campaign import (AntiAbuseStrategy,
                                                    Campaign, CampaignStatus)
    from svc.apps.marketing.models.invitation import Invitation
    from svc.apps.marketing.models.reward import (RewardRecord, RewardStrategy,
                                                  RewardType)
    from svc.apps.marketing.repositories import (CampaignRepository,
                                                 InvitationRepository,
                                                 RewardRecordRepository,
                                                 RewardStrategyRepository)
    from svc.core.database.session import get_session
    from svc.core.utils.datetime_utils import get_utc_now_without_tzinfo

# 创建日志
import logging

logging.basicConfig(level=logging.INFO, format='%(asctime)s - %(name)s - %(levelname)s - %(message)s')
logger = logging.getLogger('seed_data')

async def create_permissions(db: AsyncSession) -> List[Permission]:
    """创建权限数据"""
    # 先检查是否已存在
    result = await db.execute(select(Permission).where(Permission.name == "user:read"))
    existing = result.scalars().first()
    if existing:
        logger.info("权限数据已存在，跳过创建")
        # 返回所有现有权限
        result = await db.execute(select(Permission))
        return result.scalars().all()
    
    logger.info("创建权限数据...")
    permissions_data = [
        # 全局权限
        {"name": "*:*", "description": "所有权限（超级管理员）"},
        
        # 认证授权模块权限
        {"name": "user:read", "description": "查看用户信息"},
        {"name": "user:create", "description": "创建用户"},
        {"name": "user:update", "description": "更新用户信息"},
        {"name": "user:delete", "description": "删除用户"},
        {"name": "user:manage", "description": "管理用户（禁用/启用账户等）"},
        {"name": "role:read", "description": "查看角色信息"},
        {"name": "role:create", "description": "创建角色"},
        {"name": "role:update", "description": "更新角色"},
        {"name": "role:delete", "description": "删除角色"},
        {"name": "role:assign", "description": "分配角色给用户"},
        {"name": "permission:read", "description": "查看权限信息"},
        {"name": "permission:assign", "description": "给角色分配权限"},
        
        # 计费/订阅模块权限
        {"name": "subscription_plan:read", "description": "查看订阅计划"},
        {"name": "subscription_plan:create", "description": "创建订阅计划"},
        {"name": "subscription_plan:update", "description": "更新订阅计划"},
        {"name": "subscription_plan:delete", "description": "删除订阅计划"},
        {"name": "subscription:read", "description": "查看订阅信息"},
        {"name": "subscription:create", "description": "创建订阅"},
        {"name": "subscription:update", "description": "更新订阅"},
        {"name": "subscription:cancel", "description": "取消订阅"},
        {"name": "subscription:renew", "description": "续订订阅"},
        {"name": "invoice:read", "description": "查看发票"},
        {"name": "invoice:create", "description": "创建发票"},
        {"name": "invoice:update", "description": "更新发票"},
        {"name": "invoice:delete", "description": "删除发票"},
        {"name": "payment:read", "description": "查看支付信息"},
        {"name": "payment:process", "description": "处理支付"},
        {"name": "payment:refund", "description": "退款操作"},
        
        # 营销模块权限
        {"name": "campaign:read", "description": "查看营销活动"},
        {"name": "campaign:create", "description": "创建营销活动"},
        {"name": "campaign:update", "description": "更新营销活动"},
        {"name": "campaign:delete", "description": "删除营销活动"},
        {"name": "campaign:activate", "description": "激活/停用营销活动"},
        {"name": "invitation:read", "description": "查看邀请信息"},
        {"name": "invitation:create", "description": "创建邀请"},
        {"name": "invitation:delete", "description": "删除邀请"},
        {"name": "reward_strategy:read", "description": "查看奖励策略"},
        {"name": "reward_strategy:create", "description": "创建奖励策略"},
        {"name": "reward_strategy:update", "description": "更新奖励策略"},
        {"name": "reward_strategy:delete", "description": "删除奖励策略"},
        {"name": "reward_record:read", "description": "查看奖励记录"},
        {"name": "reward_record:create", "description": "创建奖励记录"},
        {"name": "reward_record:issue", "description": "发放奖励"},
        
        # 系统模块权限
        {"name": "config:read", "description": "查看系统配置"},
        {"name": "config:update", "description": "更新系统配置"},
        {"name": "audit_log:read", "description": "查看审计日志"},
        {"name": "audit_log:delete", "description": "删除审计日志"},
        {"name": "system:backup", "description": "系统备份操作"},
        {"name": "system:restore", "description": "系统恢复操作"},
        {"name": "system:maintenance", "description": "系统维护模式切换"},
        {"name": "system:monitor", "description": "系统监控访问权限"}
    ]
    
    permissions = []
    for perm_data in permissions_data:
        perm = Permission(**perm_data)
        db.add(perm)
        permissions.append(perm)
    
    await db.commit()
    logger.info(f"创建了{len(permissions)}个权限")
    return permissions

async def create_roles(db: AsyncSession, permissions: List[Permission]) -> List[Role]:
    """创建角色数据"""
    # 先检查是否已存在
    result = await db.execute(select(Role).where(Role.name == "admin"))
    existing = result.scalars().first()
    if existing:
        logger.info("角色数据已存在，跳过创建")
        # 返回所有现有角色
        result = await db.execute(select(Role))
        return result.scalars().all()
    
    logger.info("创建角色数据...")
    roles_data = [
        {
            "name": "admin",
            "description": "系统管理员",
            "is_system": True,
            "permissions": permissions  # 管理员拥有所有权限
        },
        {
            "name": "user",
            "description": "普通用户",
            "is_system": True,
            "permissions": [p for p in permissions if p.name.startswith("user:read")]
        },
        {
            "name": "vip",
            "description": "VIP用户",
            "is_system": False,
            "permissions": [p for p in permissions if ":read" in p.name]
        }
    ]
    
    roles = []
    for role_data in roles_data:
        role_permissions = role_data.pop("permissions")
        role = Role(**role_data)
        role.permissions = role_permissions
        db.add(role)
        roles.append(role)
    
    await db.commit()
    logger.info(f"创建了{len(roles)}个角色")
    return roles

async def create_users(db: AsyncSession, roles: List[Role]) -> List[User]:
    """创建用户数据"""
    logger.info("开始创建用户数据...")
    users_data = [
        {
            "email": "<EMAIL>",
            "password": "qinjun666",
            "username": "super",
            "fullname": "系统管理员",
            "is_superuser": True,
            "roles": [roles[0]]  # admin角色
        },
        {
            "email": "<EMAIL>",
            "password": "user123",
            "username": "user1",
            "fullname": "测试用户1",
            "roles": [roles[1]]  # user角色
        },
        {
            "email": "<EMAIL>",
            "password": "user123",
            "username": "user2",
            "fullname": "测试用户2",
            "roles": [roles[1], roles[2]]  # user + vip角色
        }
    ]
    
    users = []
    for user_data in users_data:
        # 检查用户是否已存在
        email = user_data["email"]
        result = await db.execute(select(User).where(User.email == email))
        existing = result.scalars().first()
        
        if existing:
            logger.info(f"用户 {email} 已存在，跳过创建")
            users.append(existing)
            continue
        
        # 创建新用户
        user_roles = user_data.pop("roles")
        user = await UserRepository(db=db).create(
            **user_data
        )
        
        # 设置用户角色关系
        for role in user_roles:
            stmt = insert(user_role).values(user_id=user.id, role_id=role.id)
            await db.execute(stmt)
        
        logger.info(f"成功创建用户: {email}")
        users.append(user)
    
    await db.commit()
    logger.info(f"用户创建过程完成，共有 {len(users)} 个用户")
    return users

async def create_wechat_users(db: AsyncSession, users: List[User]) -> List[WechatUser]:
    """创建微信用户数据"""
    # 先检查是否已存在
    result = await db.execute(select(WechatUser).where(WechatUser.openid == "wx_openid_111111"))
    existing = result.scalars().first()
    if existing:
        logger.info("微信用户数据已存在，跳过创建")
        # 返回所有现有微信用户
        result = await db.execute(select(WechatUser))
        return result.scalars().all()
    
    logger.info("创建微信用户数据...")
    
    # 只为部分用户创建微信账号
    wechat_users_data = [
        {
            "openid": "wx_openid_111111",
            "unionid": "wx_unionid_111111",
            "nickname": "微信用户1",
            "avatar_url": "https://example.com/avatar1.jpg",
            "gender": 1,  # 男
            "country": "中国",
            "province": "广东",
            "city": "深圳",
            "language": "zh_CN",
            "user": users[1]  # user1
        },
        {
            "openid": "wx_openid_222222",
            "unionid": "wx_unionid_222222",
            "nickname": "微信用户2",
            "avatar_url": "https://example.com/avatar2.jpg",
            "gender": 2,  # 女
            "country": "中国",
            "province": "上海",
            "city": "上海",
            "language": "zh_CN",
            "user": users[2]  # user2
        }
    ]
    
    wechat_users = []
    for wx_data in wechat_users_data:
        user = wx_data.pop("user")
        wx_user = WechatUser(**wx_data)
        wx_user.user = user
        wx_user.user_id = user.id
        db.add(wx_user)
        wechat_users.append(wx_user)
    
    await db.commit()
    logger.info(f"创建了{len(wechat_users)}个微信用户")
    return wechat_users

async def create_subscription_plans(db: AsyncSession, creator_id: int) -> List[SubscriptionPlan]:
    """
    创建订阅计划数据
    
    Args:
        db: 数据库会话
        creator_id: 创建者ID，默认为1(管理员)
    
    Returns:
        List[SubscriptionPlan]: 创建的订阅计划列表
    """
    try:
        # 先检查是否已存在
        result = await db.execute(select(SubscriptionPlan).where(SubscriptionPlan.name == "基础版"))
        existing = result.scalars().first()
        if existing:
            logger.info("订阅计划数据已存在，跳过创建")
            # 返回所有现有订阅计划
            result = await db.execute(select(SubscriptionPlan))
            return result.scalars().all()
        
        # 验证creator_id是否存在
        user_check = await db.execute(select(User).where(User.id == creator_id))
        user = user_check.scalars().first()
        if not user:
            raise ValueError(f"用户ID {creator_id} 不存在，无法创建订阅计划")
        
        logger.info(f"创建订阅计划数据，创建者ID: {creator_id}...")
        
        plans_data = [
            {
                "name": "基础版",
                "description": "适合个人用户",
                "price": 99.00,
                "currency": "CNY",
                "interval": "month",
                "tier": "basic",
                "max_users": 1,
                "max_storage": 1024,  # 1GB
                "max_projects": 3,
                "features": {"api_calls": 1000, "support": "email"},
                "user_id": creator_id
            },
            {
                "name": "专业版",
                "description": "适合小型团队",
                "price": 299.00,
                "currency": "CNY",
                "interval": "month",
                "tier": "premium",
                "max_users": 5,
                "max_storage": 5120,  # 5GB
                "max_projects": 10,
                "features": {"api_calls": 5000, "support": "priority"},
                "user_id": creator_id
            },
            {
                "name": "企业版",
                "description": "适合大型企业",
                "price": 999.00,
                "currency": "CNY",
                "interval": "month",
                "tier": "enterprise",
                "max_users": 20,
                "max_storage": 20480,  # 20GB
                "max_projects": 50,
                "features": {"api_calls": "unlimited", "support": "24/7"},
                "user_id": creator_id
            }
        ]
        
        plans = []
        plan_repo = SubscriptionPlanRepository(db=db)
        for idx, plan_data in enumerate(plans_data, 1):
            try:
                # 使用仓库模式创建订阅计划，确保数据一致性
                plan = await plan_repo.create(
                    user_id=plan_data["user_id"],
                    name=plan_data["name"],
                    description=plan_data.get("description"),
                    price=plan_data["price"],
                    currency=plan_data.get("currency", "CNY"),
                    interval=plan_data.get("interval", "month"),
                    features=plan_data.get("features", {}),
                    tier=plan_data.get("tier"),
                    max_users=plan_data.get("max_users"),
                    max_storage=plan_data.get("max_storage"),
                    max_projects=plan_data.get("max_projects")
                )
                logger.info(f"创建订阅计划 {idx}/{len(plans_data)}: {plan.name} (ID: {plan.id})")
                plans.append(plan)
            except Exception as e:
                logger.error(f"创建订阅计划 '{plan_data['name']}' 失败: {str(e)}")
                raise
        
        await db.commit()
        logger.info(f"成功创建了{len(plans)}个订阅计划")
        return plans
    except Exception as e:
        logger.error(f"创建订阅计划时发生错误: {str(e)}", exc_info=True)
        await db.rollback()
        raise

async def create_campaigns(session: AsyncSession, admin_user_id: int) -> list[int]:
    """
    创建营销活动。
    """
    logging.info("开始创建营销活动...")
    
    try:
        campaign_repo = CampaignRepository(db=session)
        campaign_ids = []
        
        # 创建邀请新用户活动
        invite_campaign = Campaign(
            name="邀请新用户",
            description="邀请新用户注册并使用我们的服务",
            start_date=get_utc_now_without_tzinfo(),
            end_date=get_utc_now_without_tzinfo() + timedelta(days=180),
            status=CampaignStatus.ACTIVE,
            creator_id=admin_user_id,
            anti_abuse_strategy=AntiAbuseStrategy.BOTH
        )
        # 直接添加到会话，而不是使用repository的create方法
        session.add(invite_campaign)
        await session.flush()
        campaign_ids.append(invite_campaign.id)
        logging.info(f"创建营销活动成功: {invite_campaign.name}, ID: {invite_campaign.id}")
        
        # 创建首次购买奖励活动
        first_purchase_campaign = Campaign(
            name="首次购买奖励",
            description="奖励首次在平台上购买的用户",
            start_date=get_utc_now_without_tzinfo(),
            end_date=get_utc_now_without_tzinfo() + timedelta(days=90),
            status=CampaignStatus.ACTIVE,
            creator_id=admin_user_id,
            anti_abuse_strategy=AntiAbuseStrategy.BOTH
        )
        # 直接添加到会话，而不是使用repository的create方法
        session.add(first_purchase_campaign)
        await session.flush()
        campaign_ids.append(first_purchase_campaign.id)
        logging.info(f"创建营销活动成功: {first_purchase_campaign.name}, ID: {first_purchase_campaign.id}")
        
        # 创建VIP会员专享活动
        vip_campaign = Campaign(
            name="VIP会员专享",
            description="VIP会员专享活动与优惠",
            start_date=get_utc_now_without_tzinfo(),
            end_date=get_utc_now_without_tzinfo() + timedelta(days=120),
            status=CampaignStatus.ACTIVE,
            creator_id=admin_user_id,
            anti_abuse_strategy=AntiAbuseStrategy.BOTH
        )
        # 直接添加到会话，而不是使用repository的create方法
        session.add(vip_campaign)
        await session.flush()
        campaign_ids.append(vip_campaign.id)
        logging.info(f"创建营销活动成功: {vip_campaign.name}, ID: {vip_campaign.id}")
        
        logging.info(f"成功创建 {len(campaign_ids)} 个营销活动")
        return campaign_ids
    except Exception as e:
        logging.error(f"创建营销活动失败: {str(e)}")
        raise

async def create_reward_strategies(db: AsyncSession, campaigns: List[Campaign], creator_id: int) -> List[RewardStrategy]:
    """
    创建奖励策略数据
    
    Args:
        db: 数据库会话
        campaigns: 活动列表
        creator_id: 创建者ID
    
    Returns:
        List[RewardStrategy]: 创建的奖励策略列表
    """
    try:
        # 先检查是否已存在
        result = await db.execute(select(RewardStrategy).where(RewardStrategy.name == "邀请人奖励"))
        existing = result.scalars().first()
        if existing:
            logger.info("奖励策略数据已存在，跳过创建")
            # 返回所有现有策略
            result = await db.execute(select(RewardStrategy))
            return result.scalars().all()
        
        if not campaigns or len(campaigns) < 3:
            raise ValueError(f"需要至少3个营销活动来创建奖励策略，当前只有{len(campaigns) if campaigns else 0}个")
        
        logger.info("创建奖励策略数据...")
        
        # 初始化策略仓库
        reward_strategy_repo = RewardStrategyRepository(db=db)
        
        # 为每个活动创建奖励策略
        reward_strategies_data = [
            # 活动1的策略
            {
                "campaign_id": campaigns[0].id,
                "name": "邀请人奖励",
                "description": "邀请好友注册成功后，邀请人获得的奖励",
                "reward_type": RewardType.FIXED,
                "is_for_inviter": True,
                "is_for_invitee": False,
                "base_reward": 50.0,  # 固定奖励50元
                "min_invitations": 1,
                "max_rewards": 5
            },
            {
                "campaign_id": campaigns[0].id,
                "name": "受邀人奖励",
                "description": "被邀请注册成功后，受邀人获得的奖励",
                "reward_type": RewardType.FIXED,
                "is_for_inviter": False,
                "is_for_invitee": True,
                "base_reward": 20.0,  # 固定奖励20元
                "min_invitations": None,
                "max_rewards": 1
            },
            # 活动2的策略
            {
                "campaign_id": campaigns[1].id,
                "name": "春季推广邀请奖励",
                "description": "春季推广活动邀请奖励",
                "reward_type": RewardType.TIERED,
                "is_for_inviter": True,
                "is_for_invitee": False,
                "base_reward": 30.0,
                "tiered_config": json.dumps([
                    {"threshold": 3, "reward": 50.0},
                    {"threshold": 5, "reward": 100.0},
                    {"threshold": 10, "reward": 200.0}
                ]),
                "min_invitations": 1,
                "max_rewards": 10
            },
            {
                "campaign_id": campaigns[1].id,
                "name": "春季推广受邀奖励",
                "description": "春季推广活动受邀奖励",
                "reward_type": RewardType.FIXED,
                "is_for_inviter": False,
                "is_for_invitee": True,
                "base_reward": 30.0,
                "min_invitations": None,
                "max_rewards": 1
            },
            # 活动3的策略
            {
                "campaign_id": campaigns[2].id,
                "name": "VIP会员专享奖励",
                "description": "VIP专享活动奖励",
                "reward_type": RewardType.PERCENTAGE,
                "is_for_inviter": True,
                "is_for_invitee": True,
                "base_reward": 10.0,
                "percentage_rate": 15.0,  # 15%的额外奖励
                "min_invitations": 1,
                "max_rewards": 3
            }
        ]
        
        reward_strategies = []
        for idx, strategy_data in enumerate(reward_strategies_data, 1):
            try:
                # 验证campaign_id
                campaign_id = strategy_data["campaign_id"]
                campaign_check = await db.execute(select(Campaign).where(Campaign.id == campaign_id))
                if not campaign_check.scalars().first():
                    logger.warning(f"策略 '{strategy_data['name']}' 引用的活动ID {campaign_id} 不存在，跳过创建")
                    continue
                
                # 使用RewardStrategyRepository创建策略
                strategy = await reward_strategy_repo.create_strategy(
                    campaign_id=strategy_data["campaign_id"],
                    name=strategy_data["name"],
                    description=strategy_data.get("description"),
                    reward_type=strategy_data["reward_type"],
                    is_for_inviter=strategy_data["is_for_inviter"],
                    is_for_invitee=strategy_data["is_for_invitee"],
                    base_reward=strategy_data["base_reward"],
                    percentage_rate=strategy_data.get("percentage_rate"),
                    tiered_config=strategy_data.get("tiered_config"),
                    min_invitations=strategy_data.get("min_invitations"),
                    max_rewards=strategy_data.get("max_rewards")
                )
                
                logger.info(f"创建奖励策略 {idx}/{len(reward_strategies_data)}: {strategy.name} (ID: {strategy.id})")
                reward_strategies.append(strategy)
            except Exception as e:
                logger.error(f"创建奖励策略 '{strategy_data['name']}' 失败: {str(e)}")
                raise
        
        await db.commit()
        logger.info(f"成功创建了{len(reward_strategies)}个奖励策略")
        return reward_strategies
    except Exception as e:
        logger.error(f"创建奖励策略时发生错误: {str(e)}", exc_info=True)
        await db.rollback()
        raise

async def create_invitations(db: AsyncSession, campaigns: List[Campaign], users: List[User]) -> List[Invitation]:
    """
    创建邀请记录数据
    
    Args:
        db: 数据库会话
        campaigns: 活动列表
        users: 用户列表
    
    Returns:
        List[Invitation]: 创建的邀请记录列表
    """
    try:
        # 先检查是否已存在
        result = await db.execute(select(Invitation).where(Invitation.code == "INV123456"))
        existing = result.scalars().first()
        if existing:
            logger.info("邀请记录数据已存在，跳过创建")
            # 返回所有现有邀请
            result = await db.execute(select(Invitation))
            return result.scalars().all()
        
        # 验证输入数据
        if not campaigns or len(campaigns) < 2:
            raise ValueError(f"需要至少2个营销活动来创建邀请记录，当前只有{len(campaigns) if campaigns else 0}个")
        
        if not users or len(users) < 3:
            raise ValueError(f"需要至少3个用户来创建邀请记录，当前只有{len(users) if users else 0}个")
        
        logger.info("创建邀请记录数据...")
        
        now = get_utc_now_without_tzinfo()
        
        # 创建邀请记录
        invitations_data = [
            # user1邀请user2(已使用)
            {
                "campaign_id": campaigns[0].id,
                "inviter_id": users[1].id,  # user1
                "invitee_id": users[2].id,  # user2
                "code": "INV123456",
                "is_used": True,
                "used_at": now - timedelta(days=15),
                "invitee_ip": "*************",
                "invitee_device": "iPhone 13",
                "opened_count": 2
            },
            # user2邀请其他用户(未使用)
            {
                "campaign_id": campaigns[0].id,
                "inviter_id": users[2].id,  # user2
                "invitee_id": None,  # 未被使用
                "code": "INV234567",
                "is_used": False,
                "used_at": None,
                "invitee_ip": None,
                "invitee_device": None,
                "opened_count": 5
            },
            # user1在另一个活动中的邀请
            {
                "campaign_id": campaigns[1].id,
                "inviter_id": users[1].id,  # user1
                "invitee_id": None,  # 未被使用
                "code": "SPR123456",
                "is_used": False,
                "used_at": None,
                "invitee_ip": None,
                "invitee_device": None,
                "opened_count": 3
            }
        ]
        
        invitations = []
        for idx, invitation_data in enumerate(invitations_data, 1):
            try:
                # 验证外键ID是否存在
                campaign_id = invitation_data["campaign_id"]
                campaign_check = await db.execute(select(Campaign).where(Campaign.id == campaign_id))
                if not campaign_check.scalars().first():
                    logger.warning(f"邀请记录 #{idx} 引用的活动ID {campaign_id} 不存在，跳过创建")
                    continue
                
                inviter_id = invitation_data["inviter_id"]
                inviter_check = await db.execute(select(User).where(User.id == inviter_id))
                if not inviter_check.scalars().first():
                    logger.warning(f"邀请记录 #{idx} 引用的邀请人ID {inviter_id} 不存在，跳过创建")
                    continue
                
                # 如果有被邀请人，也检查ID
                invitee_id = invitation_data["invitee_id"]
                if invitee_id is not None:
                    invitee_check = await db.execute(select(User).where(User.id == invitee_id))
                    if not invitee_check.scalars().first():
                        logger.warning(f"邀请记录 #{idx} 引用的被邀请人ID {invitee_id} 不存在，跳过创建")
                        continue
                
                invitation = Invitation(**invitation_data)
                db.add(invitation)
                await db.flush()
                logger.info(f"创建邀请记录 {idx}/{len(invitations_data)}: {invitation.code} (ID: {invitation.id})")
                invitations.append(invitation)
            except Exception as e:
                logger.error(f"创建邀请记录 #{idx} 失败: {str(e)}")
                raise
        
        await db.commit()
        logger.info(f"成功创建了{len(invitations)}个邀请记录")
        return invitations
    except Exception as e:
        logger.error(f"创建邀请记录时发生错误: {str(e)}", exc_info=True)
        await db.rollback()
        raise

async def create_reward_records(db: AsyncSession, campaigns: List[Campaign], users: List[User], invitations: List[Invitation], reward_strategies: List[RewardStrategy]) -> List[RewardRecord]:
    """
    创建奖励记录数据
    
    Args:
        db: 数据库会话
        campaigns: 活动列表
        users: 用户列表
        invitations: 邀请记录列表
        reward_strategies: 奖励策略列表
    
    Returns:
        List[RewardRecord]: 创建的奖励记录列表
    """
    try:
        # 先检查是否已存在
        if not invitations or not invitations[0]:
            raise ValueError("缺少邀请记录，无法创建奖励记录")
        
        # 如果至少有一个邀请记录，检查是否已存在奖励记录
        check_invitation = invitations[0]
        result = await db.execute(select(RewardRecord).where(
            (RewardRecord.campaign_id == check_invitation.campaign_id) & 
            (RewardRecord.user_id == check_invitation.inviter_id)
        ))
        existing = result.scalars().first()
        if existing:
            logger.info("奖励记录数据已存在，跳过创建")
            # 返回所有现有奖励记录
            result = await db.execute(select(RewardRecord))
            return result.scalars().all()
        
        # 验证输入数据
        if not campaigns or len(campaigns) < 2:
            raise ValueError(f"需要至少2个营销活动来创建奖励记录，当前只有{len(campaigns) if campaigns else 0}个")
        
        if not users or len(users) < 2:
            raise ValueError(f"需要至少2个用户来创建奖励记录，当前只有{len(users) if users else 0}个")
        
        if not reward_strategies or len(reward_strategies) < 3:
            raise ValueError(f"需要至少3个奖励策略来创建奖励记录，当前只有{len(reward_strategies) if reward_strategies else 0}个")
        
        logger.info("创建奖励记录数据...")
        
        now = get_utc_now_without_tzinfo()
        
        # 获取已使用的邀请
        used_invitation = None
        for inv in invitations:
            if inv.is_used:
                used_invitation = inv
                break
        
        if not used_invitation:
            logger.warning("没有找到已使用的邀请记录，将使用第一个邀请记录")
            used_invitation = invitations[0]
        
        reward_records_data = [
            # 邀请人的奖励(已发放)
            {
                "campaign_id": used_invitation.campaign_id,
                "invitation_id": used_invitation.id,
                "strategy_id": reward_strategies[0].id,  # 对应"邀请人奖励"策略
                "user_id": used_invitation.inviter_id,
                "reward_type": "cash",
                "reward_value": 50.0,
                "reward_description": "成功邀请好友注册",
                "is_issued": True,
                "issued_at": now - timedelta(days=14)
            },
            # 被邀请人的奖励(已发放)
            {
                "campaign_id": used_invitation.campaign_id,
                "invitation_id": used_invitation.id,
                "strategy_id": reward_strategies[1].id,  # 对应"受邀人奖励"策略
                "user_id": used_invitation.invitee_id if used_invitation.invitee_id else users[2].id,
                "reward_type": "cash",
                "reward_value": 20.0,
                "reward_description": "接受邀请注册",
                "is_issued": True,
                "issued_at": now - timedelta(days=14)
            },
            # 另一个活动中的奖励(未发放)
            {
                "campaign_id": campaigns[1].id,
                "invitation_id": None,
                "strategy_id": reward_strategies[2].id,  # 对应"春季推广邀请奖励"策略
                "user_id": users[1].id,  # user1
                "reward_type": "cash",
                "reward_value": 30.0,
                "reward_description": "春季活动特别奖励",
                "is_issued": False,
                "issued_at": None
            }
        ]
        
        reward_records = []
        for idx, record_data in enumerate(reward_records_data, 1):
            try:
                # 验证外键ID
                campaign_id = record_data["campaign_id"]
                campaign_check = await db.execute(select(Campaign).where(Campaign.id == campaign_id))
                if not campaign_check.scalars().first():
                    logger.warning(f"奖励记录 #{idx} 引用的活动ID {campaign_id} 不存在，跳过创建")
                    continue
                
                if record_data["invitation_id"]:
                    invitation_id = record_data["invitation_id"]
                    invitation_check = await db.execute(select(Invitation).where(Invitation.id == invitation_id))
                    if not invitation_check.scalars().first():
                        logger.warning(f"奖励记录 #{idx} 引用的邀请ID {invitation_id} 不存在，跳过创建")
                        continue
                
                strategy_id = record_data["strategy_id"]
                strategy_check = await db.execute(select(RewardStrategy).where(RewardStrategy.id == strategy_id))
                if not strategy_check.scalars().first():
                    logger.warning(f"奖励记录 #{idx} 引用的策略ID {strategy_id} 不存在，跳过创建")
                    continue
                
                user_id = record_data["user_id"]
                user_check = await db.execute(select(User).where(User.id == user_id))
                if not user_check.scalars().first():
                    logger.warning(f"奖励记录 #{idx} 引用的用户ID {user_id} 不存在，跳过创建")
                    continue
                
                record = RewardRecord(**record_data)
                db.add(record)
                await db.flush()
                logger.info(f"创建奖励记录 {idx}/{len(reward_records_data)}: {record.reward_description} (ID: {record.id})")
                reward_records.append(record)
            except Exception as e:
                logger.error(f"创建奖励记录 #{idx} 失败: {str(e)}")
                raise
        
        await db.commit()
        logger.info(f"成功创建了{len(reward_records)}个奖励记录")
        return reward_records
    except Exception as e:
        logger.error(f"创建奖励记录时发生错误: {str(e)}", exc_info=True)
        await db.rollback()
        raise

async def main():
    """主函数，初始化所有数据"""
    try:
        # 创建数据库会话
        async with get_session() as db:
            logger.info("开始初始化数据...")
            
            # 创建权限
            permissions = await create_permissions(db)
            
            # 创建角色
            roles = await create_roles(db, permissions)
            
            # 创建用户
            users = await create_users(db, roles)
            
            # 获取管理员用户ID
            if not users:
                raise ValueError("用户创建失败，无法获取管理员ID")
            
            # 假设第一个用户是管理员
            admin_user = users[0]
            admin_id = admin_user.id
            logger.info(f"使用管理员用户ID: {admin_id} (用户名: {admin_user.username})")
            
            # 创建微信用户
            # wechat_users = await create_wechat_users(db, users)
            
            # 创建订阅计划（使用真实的管理员ID作为创建者ID）
            plans = await create_subscription_plans(db, creator_id=admin_id)
            
            # 验证管理员用户存在
            admin_check = await db.execute(select(User).where(User.id == admin_id))
            admin_exists = admin_check.scalars().first()
            if not admin_exists:
                raise ValueError(f"管理员用户ID {admin_id} 不存在，无法继续创建依赖管理员的数据")
            
            # 创建营销活动（使用真实的管理员ID作为创建者ID）
            campaign_ids = await create_campaigns(db, admin_id)
            
            # 获取Campaign对象列表
            campaigns = []
            for campaign_id in campaign_ids:
                campaign_result = await db.execute(select(Campaign).where(Campaign.id == campaign_id))
                campaign_obj = campaign_result.scalars().first()
                if campaign_obj:
                    campaigns.append(campaign_obj)
            
            # 创建奖励策略
            reward_strategies = await create_reward_strategies(db, campaigns, admin_id)
            
            # 创建邀请记录
            invitations = await create_invitations(db, campaigns, users)
            
            # 创建奖励记录
            reward_records = await create_reward_records(db, campaigns, users, invitations, reward_strategies)
            
            logger.info("数据初始化完成！")
            
    except Exception as e:
        logger.error(f"初始化数据时发生错误: {str(e)}", exc_info=True)
        raise

if __name__ == "__main__":
    asyncio.run(main()) 