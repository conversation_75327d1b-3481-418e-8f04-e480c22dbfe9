from typing import Optional, Dict, Any, List
from datetime import datetime

from pydantic import BaseModel, Field, field_validator, ConfigDict
from svc.core.schemas.base import PaginatedResponse


# 参数模型
class GetInvoiceParams(BaseModel):
    """获取账单参数"""
    model_config = ConfigDict(
        str_strip_whitespace=True,
        json_schema_extra={
            "example": {
                "invoice_id": 1,
                "user_id": None
            }
        }
    )
    
    invoice_id: int = Field(description="账单ID")
    user_id: Optional[int] = Field(default=None, description="用户ID")


class GetInvoicesParams(BaseModel):
    """获取账单列表参数"""
    model_config = ConfigDict(
        str_strip_whitespace=True,
        json_schema_extra={
            "example": {
                "subscription_id": 1,
                "user_id": 1,
                "status": "unpaid",
                "page_num": 1,
                "page_size": 10
            }
        }
    )
    
    subscription_id: Optional[int] = Field(default=None, description="订阅ID")
    user_id: Optional[int] = Field(default=None, description="用户ID")
    status: Optional[str] = Field(default=None, description="账单状态")
    page_num: int = Field(default=1, ge=1, description="页码")
    page_size: int = Field(default=10, ge=1, le=1000, description="每页数量")


class GetUnpaidInvoicesParams(BaseModel):
    """获取未支付账单参数"""
    model_config = ConfigDict(
        str_strip_whitespace=True,
        json_schema_extra={
            "example": {
                "subscription_id": 1
            }
        }
    )
    
    subscription_id: int = Field(description="订阅ID")


class CreateInvoiceParams(BaseModel):
    """创建账单参数"""
    model_config = ConfigDict(
        str_strip_whitespace=True,
        json_schema_extra={
            "example": {
                "invoice_data": {}
            }
        }
    )
    
    invoice_data: 'InvoiceCreate' = Field(description="账单创建数据")


class UpdateInvoiceParams(BaseModel):
    """更新账单参数"""
    model_config = ConfigDict(
        str_strip_whitespace=True,
        json_schema_extra={
            "example": {
                "invoice_id": 1,
                "invoice_data": {}
            }
        }
    )
    
    invoice_id: int = Field(description="账单ID")
    invoice_data: 'InvoiceUpdate' = Field(description="账单更新数据")


class MarkAsPaidParams(BaseModel):
    """标记为已支付参数"""
    model_config = ConfigDict(
        str_strip_whitespace=True,
        json_schema_extra={
            "example": {
                "invoice_id": 1,
                "paid_at": "2024-03-24T12:00:00"
            }
        }
    )
    
    invoice_id: int = Field(description="账单ID")
    paid_at: Optional[datetime] = Field(default=None, description="支付时间")


class MarkAsOverdueParams(BaseModel):
    """标记为逾期参数"""
    model_config = ConfigDict(
        str_strip_whitespace=True,
        json_schema_extra={
            "example": {
                "invoice_id": 1
            }
        }
    )
    
    invoice_id: int = Field(description="账单ID")


class CancelInvoiceParams(BaseModel):
    """取消账单参数"""
    model_config = ConfigDict(
        str_strip_whitespace=True,
        json_schema_extra={
            "example": {
                "invoice_id": 1
            }
        }
    )
    
    invoice_id: int = Field(description="账单ID")


# 为了导入兼容性，添加别名
MarkInvoicePaidParams = MarkAsPaidParams
MarkInvoiceOverdueParams = MarkAsOverdueParams


class InvoiceBase(BaseModel):
    """账单基础模型"""
    model_config = ConfigDict(
        str_strip_whitespace=True,
        json_schema_extra={
            "example": {
                "subscription_id": 1,
                "amount": 99.00,
                "currency": "CNY",
                "description": "月度订阅费用",
                "due_date": "2024-03-24T12:00:00",
                "metadata": {}
            }
        }
    )
    
    subscription_id: int = Field(description="订阅ID")
    amount: float = Field(description="账单金额")
    currency: str = Field(default="CNY", description="货币单位")
    description: Optional[str] = Field(default=None, description="账单描述")
    due_date: Optional[datetime] = Field(default=None, description="到期时间")
    metadata: Dict[str, Any] = Field(default_factory=dict, description="元数据")


class InvoiceCreate(InvoiceBase):
    """创建账单请求模型"""
    pass


class InvoiceUpdate(BaseModel):
    """更新账单请求模型"""
    model_config = ConfigDict(
        str_strip_whitespace=True,
        json_schema_extra={
            "example": {
                "amount": 99.00,
                "description": "月度订阅费用",
                "due_date": "2024-03-24T12:00:00",
                "metadata": {}
            }
        }
    )
    
    amount: Optional[float] = Field(default=None, description="账单金额")
    description: Optional[str] = Field(default=None, description="账单描述")
    due_date: Optional[datetime] = Field(default=None, description="到期时间")
    metadata: Optional[Dict[str, Any]] = Field(default=None, description="元数据")


class InvoiceInDB(InvoiceBase):
    """数据库中的账单模型"""
    model_config = ConfigDict(
        from_attributes=True,
        str_strip_whitespace=True,
        json_schema_extra={
            "example": {
                "id": 1,
                "status": "unpaid",
                "invoice_number": "INV20240324001",
                "invoice_date": "2024-03-24T12:00:00",
                "is_paid": False,
                "paid_at": None,
                "created_at": "2024-03-24T12:00:00",
                "updated_at": "2024-03-24T12:00:00"
            }
        }
    )
    
    id: int = Field(description="账单ID")
    status: str = Field(description="账单状态")
    invoice_number: str = Field(description="账单编号")
    invoice_date: datetime = Field(description="账单日期")
    is_paid: bool = Field(description="是否已支付")
    paid_at: Optional[datetime] = Field(default=None, description="支付时间")
    created_at: datetime = Field(description="创建时间")
    updated_at: datetime = Field(description="更新时间")
    
    @field_validator('metadata', mode='before')
    @classmethod
    def validate_metadata(cls, v, info):
        """将meta_data字段映射到metadata"""
        if hasattr(info.data, 'meta_data'):
            return info.data.meta_data
        return v


class InvoiceResponse(InvoiceInDB):
    """账单响应模型"""
    pass


class InvoiceListResponse(PaginatedResponse[InvoiceResponse]):
    """账单列表响应模型"""
    pass 