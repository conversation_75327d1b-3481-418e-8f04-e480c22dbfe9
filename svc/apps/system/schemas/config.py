"""
配置项模式。
提供系统配置项的数据验证和序列化模式。
"""

from typing import Optional
from datetime import datetime
from pydantic import BaseModel, Field, field_serializer, ConfigDict
from svc.core.schemas.base import PaginatedResponse

# 基础模型
class ConfigItemBase(BaseModel):
    """配置项基础模型"""
    key: str = Field(..., description="配置项键", min_length=1, max_length=100)
    value: str = Field(..., description="配置项值", min_length=1)
    description: Optional[str] = Field(None, description="配置项描述")

# 创建请求模型
class ConfigItemCreate(ConfigItemBase):
    """配置项创建请求模型"""
    pass

# 更新请求模型
class ConfigItemUpdate(BaseModel):
    """配置项更新请求模型"""
    value: Optional[str] = Field(None, description="配置项值", min_length=1)
    description: Optional[str] = Field(None, description="配置项描述")

# 数据库模型转换为API响应模型
class ConfigItemResponse(ConfigItemBase):
    """配置项响应模型"""
    id: int = Field(..., description="配置项ID")
    created_at: Optional[datetime] = Field(None, description="创建时间")
    updated_at: Optional[datetime] = Field(None, description="更新时间")
    
    @field_serializer('created_at', 'updated_at')
    def serialize_datetime(self, dt: Optional[datetime]) -> Optional[str]:
        return dt.isoformat() if dt else None
    
    model_config = ConfigDict(
        from_attributes=True
    )

class ConfigItemCreate(BaseModel):
    """配置项创建模型"""
    key: str = Field(..., description="配置项键", min_length=1, max_length=100)
    value: str = Field(..., description="配置项值", min_length=1)
    description: str = Field("", description="配置项描述")

class ConfigItemUpdate(BaseModel):
    """配置项更新模型"""
    value: str = Field(..., description="配置项值", min_length=1)
    description: str = Field(None, description="配置项描述")

class ConfigItemResponse(BaseModel):
    """配置项响应模型"""
    id: int = Field(..., description="配置项ID")
    key: str = Field(..., description="配置项键")
    value: str = Field(..., description="配置项值")
    description: str = Field("", description="配置项描述")

class ConfigListResponse(PaginatedResponse[ConfigItemResponse]):
    """配置项列表响应模型"""
    pass