import uuid
from fastapi import Request, Response
from starlette.types import ASGIApp
from .base import BaseMiddleware

class RequestIdMiddleware(BaseMiddleware):
    """请求ID中间件，为每个请求生成唯一ID"""
    
    priority = 10  # 最高优先级

    __name__ = "request_id"
    
    def __init__(
        self,
        app: ASGIApp,
        enabled: bool = True,
        header_name: str = "X-Request-ID",
        **options
    ):
        super().__init__(app, enabled=enabled, **options)
        self.header_name = header_name
    
    async def dispatch(self, request: Request, call_next) -> Response:
        if not self.is_enabled():
            return await call_next(request)
            
        # 检查请求头中是否已有请求ID
        request_id = request.headers.get(self.header_name)
        
        # 如果没有，生成新的请求ID
        if not request_id:
            request_id = str(uuid.uuid4())
        
        # 将请求ID存储在请求状态中
        request.state.request_id = request_id
        
        # 处理请求
        response = await call_next(request)
        
        # 在响应头中添加请求ID
        response.headers[self.header_name] = request_id
        
        return response 