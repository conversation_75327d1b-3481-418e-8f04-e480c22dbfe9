"""
配置仓储实现。
负责配置模型的数据库访问操作，实现数据访问与业务逻辑分离。
"""

from typing import Optional, List, Dict, Any, Tuple
from sqlalchemy.ext.asyncio import AsyncSession
from sqlalchemy.future import select
from sqlalchemy import or_, and_, func, asc, desc

from svc.core.repositories.base import BaseRepository
from svc.apps.system.models.config_item import ConfigItem
from svc.apps.system.schemas.config import ConfigItemCreate, ConfigItemUpdate

class ConfigRepository(BaseRepository[ConfigItem, ConfigItemCreate, ConfigItemUpdate]):
    """配置仓库类，提供配置项数据访问方法"""
    
    def __init__(self):
        """初始化配置仓库"""
        super().__init__(ConfigItem)
    
    async def get_by_key(self, db: AsyncSession, key: str) -> Optional[ConfigItem]:
        """
        通过键获取配置项
        
        Args:
            db: 数据库会话
            key: 配置项键
            
        Returns:
            Optional[ConfigItem]: 配置项，不存在时返回None
        """
        result = await db.execute(
            select(self.model).where(self.model.key == key)
        )
        return result.scalars().first()
    
    async def get_by_prefix(self, db: AsyncSession, prefix: str) -> List[ConfigItem]:
        """
        通过前缀获取配置项
        
        Args:
            db: 数据库会话
            prefix: 键前缀
            
        Returns:
            List[ConfigItem]: 匹配前缀的配置项列表
        """
        if not prefix:
            return await self.get_list(db)
        
        result = await db.execute(
            select(self.model).where(self.model.key.like(f"{prefix}%"))
        )
        return result.scalars().all()
    
    async def update_by_prefix(self, db: AsyncSession, prefix: str, new_value: str) -> Tuple[int, List[ConfigItem]]:
        """
        根据前缀批量更新配置项
        
        Args:
            db: 数据库会话
            prefix: 键前缀
            new_value: 新值
            
        Returns:
            Tuple[int, List[ConfigItem]]: 更新数量和更新后的配置项列表
        """
        # 查找匹配前缀的配置项
        items = await self.get_by_prefix(db, prefix)
        updated_items = []
        
        # 逐个更新
        for item in items:
            item.value = new_value
            updated_items.append(item)
        
        # 如果使用SQLAlchemy 2.0，这里可以使用批量更新API
        # 例如：await db.execute(update(self.model).where(...).values(value=new_value))
        
        # 提交更改
        if updated_items:
            await db.flush()
        
        return len(updated_items), updated_items 