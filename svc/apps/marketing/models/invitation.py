"""
邀请模型，记录邀请关系和状态
"""
from sqlalchemy import Column, BigInteger, String, DateTime, Boolean, Integer, ForeignKey
from sqlalchemy.orm import relationship
from typing import Optional

from svc.core.database.session import Base
from svc.core.utils.datetime_utils import get_utc_now_without_tzinfo

class Invitation(Base):
    """
    邀请记录模型，记录邀请关系和状态
    """
    __tablename__ = "invitations"
    
    id = Column(BigInteger, primary_key=True, autoincrement=True)
    campaign_id = Column(BigInteger, ForeignKey("campaigns.id", ondelete="CASCADE"), nullable=False)
    
    # 邀请人和被邀请人
    inviter_id = Column(BigInteger, ForeignKey("users.id", ondelete="CASCADE"), nullable=False)
    invitee_id = Column(BigInteger, ForeignKey("users.id", ondelete="SET NULL"), nullable=True)
    
    # 邀请码和状态
    code = Column(String(32), nullable=False, unique=False, index=True)
    is_used = Column(Boolean, default=False, nullable=False)
    used_at = Column(DateTime, nullable=True)
    
    # 防刷数据
    invitee_ip = Column(String(50), nullable=True)
    invitee_device = Column(String(200), nullable=True)
    
    # 统计数据
    opened_count = Column(Integer, default=0, comment="打开次数")
    
    # 创建和更新时间
    created_at = Column(DateTime, nullable=False, default=get_utc_now_without_tzinfo)
    updated_at = Column(DateTime, nullable=False, default=get_utc_now_without_tzinfo, 
                        onupdate=get_utc_now_without_tzinfo)
    
    # 关系
    campaign = relationship("Campaign", back_populates="invitations")
    inviter = relationship("User", foreign_keys=[inviter_id])
    invitee = relationship("User", foreign_keys=[invitee_id])
    reward_records = relationship("RewardRecord", back_populates="invitation")
    
    def to_dict(self) -> dict:
        """
        将模型对象转换为字典
        
        Returns:
            dict: 字典表示
        """
        return {
            "id": self.id,
            "campaign_id": self.campaign_id,
            "inviter_id": self.inviter_id,
            "invitee_id": self.invitee_id,
            "code": self.code,
            "is_used": self.is_used,
            "used_at": self.used_at.isoformat() if self.used_at else None,
            "invitee_ip": self.invitee_ip,
            "invitee_device": self.invitee_device,
            "opened_count": self.opened_count,
            "created_at": self.created_at.isoformat() if self.created_at else None,
            "updated_at": self.updated_at.isoformat() if self.updated_at else None
        } 