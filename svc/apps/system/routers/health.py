"""
健康检查路由模块。
提供系统健康状态检查的API接口。
"""

from typing import Any, Dict

from fastapi import APIRouter, Depends, HTTPException, status

from svc.apps.auth.dependencies import get_current_superuser, get_current_user
from svc.apps.auth.models.user import User
from svc.apps.system.dependencies import get_system_service
from svc.apps.system.services.system import SystemService
from svc.core.services.result import Result

# 创建路由器
router = APIRouter(tags=["系统监控"])

# === 公开健康检查接口（无需认证） ===

@router.get("/liveness", status_code=204, summary="存活检查")
async def liveness_check():
    """
    简单的存活检查

    用于Kubernetes等容器编排平台的liveness probe。
    如果服务正在运行，返回204 No Content。

    **无需认证**
    """
    # 简单返回，不需要任何检查
    return None


@router.get("/readiness", response_model=Result[Dict[str, Any]], summary="就绪检查")
async def readiness_check(
    system_service: SystemService = Depends(get_system_service)
):
    """
    就绪状态检查

    用于Kubernetes等容器编排平台的readiness probe。
    检查系统是否已准备好处理请求，包括数据库连接等依赖服务是否正常。

    **无需认证**

    **返回信息：**
    - **status**: 整体健康状态 (healthy, unhealthy, warning, unknown)
    - **services**: 各服务组件状态
    - **timestamp**: 检查时间
    """
    health_result = await system_service.get_health_status()

    # 如果系统不健康，返回503状态码
    if health_result.is_success and health_result.data.get("status") == "unhealthy":
        raise HTTPException(
            status_code=status.HTTP_503_SERVICE_UNAVAILABLE,
            detail="系统服务不可用"
        )

    return health_result


# === 需要认证的健康检查接口 ===

@router.get("", response_model=Result[Dict[str, Any]], summary="获取健康状态")
async def get_health_status(
    system_service: SystemService = Depends(get_system_service),
    current_user: User = Depends(get_current_user)  # 需要登录
):
    """
    获取系统健康状态

    返回系统的健康状态信息，包括各个服务组件的状态。

    **需要权限：** 登录用户

    **返回信息：**
    - **status**: 整体健康状态 (healthy, unhealthy, warning, unknown)
    - **services**: 各服务组件状态
    - **uptime**: 系统运行时间
    - **timestamp**: 检查时间
    """
    return await system_service.get_health_status()

@router.get("/detailed", response_model=Result[Dict[str, Any]], summary="获取详细健康状态")
async def get_detailed_health_status(
    system_service: SystemService = Depends(get_system_service),
    current_user: User = Depends(get_current_superuser)  # 需要管理员权限
):
    """
    获取详细的系统健康状态

    返回系统的详细健康状态信息，包括各个服务组件的状态和详细指标。

    **需要权限：** 超级管理员

    **返回信息：**
    - **status**: 整体健康状态 (healthy, unhealthy, warning, unknown)
    - **services**: 各服务组件状态
    - **details**: 详细检查项信息（数据库、Redis、系统资源等）
    - **uptime**: 系统运行时间
    - **timestamp**: 检查时间
    """
    return await system_service.get_health_status()


@router.get("/info", response_model=Result[Dict[str, Any]], summary="获取系统信息")
async def get_system_info(
    system_service: SystemService = Depends(get_system_service),
    current_user: User = Depends(get_current_user)  # 需要登录
):
    """
    获取系统信息

    返回系统基本信息，包括应用信息、系统信息和资源使用情况。

    **需要权限：** 登录用户

    **返回信息：**
    - **application**: 应用信息（名称、版本、环境、运行时间）
    - **system**: 系统信息（平台、Python版本、架构等）
    - **resources**: 资源使用情况
    - **timestamp**: 获取时间
    """
    return await system_service.get_system_info()


@router.get("/metrics", response_model=Result[Dict[str, Any]], summary="获取性能指标")
async def get_performance_metrics(
    system_service: SystemService = Depends(get_system_service),
    current_user: User = Depends(get_current_superuser)  # 需要管理员权限
):
    """
    获取系统性能指标

    返回详细的性能指标，包括CPU、内存、磁盘、网络等详细信息。

    **需要权限：** 超级管理员

    **返回信息：**
    - **cpu**: CPU使用率、核心数、负载
    - **memory**: 内存使用情况（总量、已用、可用、百分比）
    - **disk**: 磁盘使用情况（总量、已用、可用、百分比）
    - **network**: 网络统计（如果可用）
    - **database**: 数据库性能指标
    - **redis**: Redis性能指标（如果可用）
    - **timestamp**: 获取时间
    """
    return await system_service.get_performance_metrics()


@router.get("/services", response_model=Result[Dict[str, Any]], summary="获取服务状态")
async def get_services_status(
    system_service: SystemService = Depends(get_system_service),
    current_user: User = Depends(get_current_superuser)  # 需要管理员权限
):
    """
    获取各个服务组件的状态

    返回系统中各个服务组件的详细状态信息。

    **需要权限：** 超级管理员

    **返回信息：**
    - **database**: 数据库连接状态
    - **redis**: Redis连接状态（如果配置）
    - **system_resources**: 系统资源状态
    - **timestamp**: 检查时间
    """
    health_result = await system_service.get_health_status()

    if not health_result.is_success:
        return health_result

    # 只返回服务状态信息
    services_info = {
        "services": health_result.data.get("services", {}),
        "timestamp": health_result.data.get("timestamp")
    }

    return Result(
        is_success=True,
        result_code=200,
        result_msg="获取成功",
        data=services_info
    )