"""
通用基础模型定义
包含应用中所有模型共享的基类和工具
"""
from typing import Any, Dict, Generic, List, Optional, TypeVar, Union
from pydantic import BaseModel, ConfigDict, Field

from svc.core.utils.string_utils import to_camel_case

# 定义泛型类型变量
T = TypeVar('T')


class CamelCaseModel(BaseModel):
    """支持camelCase字段输出的基础模型"""
    model_config = ConfigDict(
        str_strip_whitespace=True,
        alias_generator=to_camel_case,
        populate_by_name=True,
    )


class PaginatedResponse(CamelCaseModel, Generic[T]):
    """通用分页响应模型"""
    model_config = ConfigDict(
        json_schema_extra={
            "example": {
                "total": 100,
                "items": [],
                "page": 1,
                "pageSize": 10,
                "totalPages": 10
            }
        }
    )
    
    total: int = Field(description="总记录数")
    items: List[T] = Field(description="数据列表")
    page: int = Field(default=1, description="当前页码")
    page_size: int = Field(default=10, description="每页大小")
    total_pages: int = Field(default=1, description="总页数") 