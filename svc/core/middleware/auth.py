from typing import Callable, List, Optional, Dict, Any
import logging
from fastapi import Request, Response
from fastapi.responses import J<PERSON><PERSON>esponse
from starlette.types import ASGIApp
from svc.core.security.oauth2 import get_token_from_header,get_token_from_request
from svc.core.security.token import TokenService
from svc.core.middleware.base import BaseMiddleware
from svc.core.config.settings import get_settings
from svc.core.cache.apps import token_cache

# 配置日志
logger = logging.getLogger(__name__)

class AuthMiddleware(BaseMiddleware):
    """认证中间件，处理HTTP请求的令牌验证和用户信息注入"""
    
    priority = 20  # 认证中间件优先级

    __name__ = "auth"
    
    def __init__(
        self, 
        app: ASGIApp,
        enabled: bool = True,
        exclude_paths: List[str] = None,
        auth_header: str = "Authorization",
        token_type: str = "Bearer",
        **options
    ):
        """初始化认证中间件
        
        Args:
            app: ASGI应用
            enabled: 是否启用
            exclude_paths: 排除的路径列表
            auth_header: 认证头名称
            token_type: 令牌类型
            options: 其他选项
        """
        super().__init__(app, enabled=enabled, **options)
        
        # 获取配置
        settings = get_settings()
        
        # 初始化令牌服务
        self.token_service = TokenService(
            secret_key=settings.secret_key,
            algorithm=settings.algorithm,
            access_token_ttl=settings.access_token_expire_minutes * 60,
            refresh_token_ttl=settings.refresh_token_expire_minutes * 60
        )
        
        # 排除路径
        self.exclude_paths = set(exclude_paths) | {
            "/docs", 
            "/redoc", 
            "/openapi.json", 
            f"{settings.api_prefix}/auth/login", 
            f"{settings.api_prefix}/auth/register",
            f"{settings.api_prefix}/wechat/login",
        }
        
        self.auth_header = auth_header
        self.token_type = token_type
        
        
        logger.info(f"授权中间件初始化，启用状态: {enabled}")
 
    async def dispatch(self, request: Request, call_next) -> Response:
        """处理请求，验证认证令牌并将身份信息注入 request.state"""
        
        # 检查中间件是否启用
        if not self.is_enabled():
            return await call_next(request)
            
        # 检查是否需要跳过认证
        if self._should_skip_auth(request):
            # 即使跳过认证，也设置一个默认值，避免后续检查 state 属性时出错
            request.state.user_id = None
            request.state.token = None
            request.state.token_payload = None
            return await call_next(request)
               
        # 获取令牌
        token = self._extract_token(request)

        
        if not token:
            # 对于需要认证的路径，如果没有令牌则拒绝
            return self._create_error_response(
                status_code=401,
                detail="缺少认证令牌"
            )
        
        # 验证令牌（先从缓存获取）
        payload = await token_cache.get(token)
        
        # 缓存未命中，验证令牌
        if payload is None:
            try:
                payload = self.token_service.verify_access_token(token)
                
                # 验证成功，加入缓存
                if payload:
                    # 使用令牌作为键，payload作为值，设置合适的 TTL
                    # 注意：TTL 应与令牌有效期相关或稍短
                    await token_cache.set(token, payload, ttl=self.token_service.access_token_ttl) 
                else: # 令牌无效或过期
                    return self._create_error_response(
                        status_code=401,
                        detail="无效或已过期的认证令牌"
                    )
            except Exception as e:
                logger.error(f"令牌验证错误: {str(e)}")
                return self._create_error_response(
                    status_code=401,
                    detail="无效的认证令牌"
                )
        
        # 提取用户ID并存入 state
        try:
            user_id = int(payload.get("sub"))
            
            # 只注入基本信息到 state
            request.state.user_id = user_id
            request.state.token = token
            request.state.token_payload = payload
            
            # 处理请求
            return await call_next(request)
                
        except (ValueError, TypeError): # 更健壮地处理 user_id 提取失败
             logger.error(f"无法从令牌负载中提取有效的用户ID: {payload}")
             return self._create_error_response(
                status_code=401,
                detail="无效的用户凭证" # 避免透露过多内部信息
            )
        except Exception as e:
            logger.error(f"认证过程中发生未知错误: {str(e)}")
            return self._create_error_response(
                status_code=500,
                detail="认证服务内部错误"
            )
    
    def _should_skip_auth(self, request: Request) -> bool:
        """检查是否需要跳过认证"""
        path = request.url.path
        
        # 使用更高效的路径检查
        return any(
            path.startswith(exclude_path.rstrip('/')) # 确保比较时路径分隔符一致
            for exclude_path in self.exclude_paths
        )
        
    def _extract_token(self, request: Request) -> Optional[str]:
        """从请求中提取令牌
        
        使用标准方式从请求头中提取令牌
        
        Args:
            request: HTTP请求对象
            
        Returns:
            提取的令牌或 None
        """
        auth_header = request.headers.get(self.auth_header) 
        return get_token_from_header(auth_header, self.token_type) # 传入 token_type
        
    def _create_error_response(
        self, status_code: int, detail: str
    ) -> JSONResponse:
        """创建统一的错误响应"""
        # 保持现有错误响应格式
        return JSONResponse(
            status_code=status_code,
            content={
                "is_success": False,
                "result_code": status_code,
                "result_msg": detail,
                "data": None
            },
            headers={"WWW-Authenticate": f"{self.token_type} realm=\"Protected Area\""} if status_code == 401 else {}
        )