"""
系统模块
包含系统配置、监控、审计日志和健康检查功能
"""

# 导出依赖函数
from svc.apps.system.dependencies import (get_audit_log_repository,
                                          get_audit_log_service,
                                          get_system_service)
# 导出ORM模型
from svc.apps.system.models.audit_log import AuditLog
from svc.apps.system.repositories.audit_log import AuditLogRepository
from svc.apps.system.schemas.audit_log import AuditLogBase  # 审计日志模型
from svc.apps.system.schemas.audit_log import (AuditLogCreate, AuditLogFilter,
                                               AuditLogInDB,
                                               AuditLogListResponse,
                                               AuditLogResponse)
from svc.apps.system.services.audit_log import AuditLogService
# 导出服务类
from svc.apps.system.services.system import SystemService
# 错误处理
from svc.core.exceptions import ErrorCode, handle_route_errors

# 导出仓库类




# 导出
__all__ = [
    # 模型
    "ConfigItem", "AuditLog",
    
    # Schema
    "ConfigItemBase", "ConfigItemCreate", "ConfigItemUpdate", "ConfigItemResponse",
    "AuditLogBase", "AuditLogCreate", "AuditLogInDB", "AuditLogResponse",
    "AuditLogListResponse", "AuditLogFilter",
    
    # 服务类
    "SystemService", "ConfigService", "AuditLogService",
    
    # 仓库类
    "ConfigRepository", "AuditLogRepository",
    
    # 依赖函数
    "get_system_service", "get_config_service", "get_config_repository",
    "get_audit_log_service", "get_audit_log_repository",
    
    # 错误处理
    "ErrorCode", "handle_route_errors"
]
