from fastapi import APIRouter, Depends, Request, Query
from typing import List, Optional, Dict, Any

from svc.core.services.result import Result, ResultFactory
from svc.apps.auth.dependencies import get_current_active_user, has_permission,get_current_superuser
from svc.apps.auth.models.user import User
from svc.apps.marketing.dependencies import get_invitation_service
from svc.apps.marketing.services.invitation import InvitationService
from svc.apps.marketing.schemas.invitation import (
    InvitationStatsResponse,
    InvitationListResponse,
    GetInvitationsParams,
)
from svc.core.exceptions import (
    handle_route_errors,
    INVITATION_ERROR_MAPPING
)

# Re-introduce the file-level router
router = APIRouter(
    tags=["邀请"] # Tag
)

# === 客户端路由 (Client Routes) ===
# Paths relative to /invitations


@router.post("/{campaign_id}", response_model=Result) 
@handle_route_errors(INVITATION_ERROR_MAPPING)
async def generate_invitation_code(
    campaign_id: int,
    invitation_service: InvitationService = Depends(get_invitation_service),
    current_user: User = Depends(get_current_active_user)
) -> Result:
    """生成邀请码/链接 (客户端)"""
    result = await invitation_service.generate_invitation_code(current_user.id, campaign_id)
    return result

@router.get("/mine", response_model=Result[InvitationListResponse]) 
@handle_route_errors(INVITATION_ERROR_MAPPING)
async def get_my_invitations(
    campaign_id: Optional[int] = Query(None, description="按活动ID过滤"),
    page_num: int = Query(1, ge=1, description="页码"),
    page_size: int = Query(20, ge=1, le=100, description="每页记录数"),
    service: InvitationService = Depends(get_invitation_service),
    current_user: User = Depends(get_current_active_user)
) -> Result[InvitationListResponse]:
    """获取当前用户的邀请列表 (客户端)"""
    result = await service.get_user_invitations(
        user_id=current_user.id, 
        campaign_id=campaign_id,
        page_num=page_num,
        page_size=page_size
    )
    return result

@router.get("/stats", response_model=Result[InvitationStatsResponse]) 
@handle_route_errors(INVITATION_ERROR_MAPPING)
async def get_my_invitation_stats(
    campaign_id: Optional[int] = None,
    invitation_service: InvitationService = Depends(get_invitation_service),
    current_user: User = Depends(get_current_active_user)
) -> Result[InvitationStatsResponse]:
    """获取当前用户的邀请统计 (客户端)"""
    stats = await invitation_service.get_invitation_stats(current_user.id, campaign_id)
    return stats

@router.get("/validate/{code}", response_model=Result) 
@handle_route_errors(INVITATION_ERROR_MAPPING)
async def validate_invitation_code(
    code: str,
    request: Request,
    invitation_service: InvitationService = Depends(get_invitation_service),
    current_user: User = Depends(get_current_superuser)
) -> Result:
    """验证邀请码 (客户端/公开)"""
    ip = request.client.host
    device_info = request.headers.get("User-Agent", "")
    validation = await invitation_service.validate_invitation_code(code, ip, device_info)
    if not validation:
        return ResultFactory.resource_not_found("邀请码", code)
    if not validation["valid"]:
        return ResultFactory.failed(code=validation.get("error_code", "INVALID_INVITATION"), message=validation["reason"])
    return ResultFactory.success(data={"message": "邀请码有效"})

@router.post("/complete/{code}", response_model=Result) 
@handle_route_errors(INVITATION_ERROR_MAPPING)
async def complete_invitation(
    code: str,
    request: Request,
    invitation_service: InvitationService = Depends(get_invitation_service),
    current_user: User = Depends(get_current_active_user)
) -> Result:
    """完成邀请 (客户端 - 被邀请人操作)"""
    ip = request.client.host
    device_info = request.headers.get("User-Agent", "")
    result = await invitation_service.complete_invitation(code, current_user.id, ip, device_info)
    return result

# === 管理端路由 (Admin Routes) ===
# Paths prefixed with /admin within this router

@router.get("/admin/", response_model=Result[InvitationListResponse]) 
@handle_route_errors(INVITATION_ERROR_MAPPING)
async def admin_list_invitations(
    page_num: int = Query(1, ge=1, description="页码"),
    page_size: int = Query(10, ge=1, le=1000, description="每页数量"),
    campaign_id: Optional[int] = Query(None, description="活动ID过滤"),
    inviter_id: Optional[int] = Query(None, description="邀请人ID过滤"),
    is_used: Optional[bool] = Query(None, description="是否已使用过滤"),
    invitation_service: InvitationService = Depends(get_invitation_service),
    current_user: User = Depends(get_current_active_user),
    _: bool = Depends(lambda: has_permission("invitation:read"))
) -> Result[InvitationListResponse]:
    """获取邀请列表 (管理端)"""
    params = GetInvitationsParams(
        page_num=page_num,
        page_size=page_size,
        campaign_id=campaign_id,
        inviter_id=inviter_id,
        is_used=is_used
    )
    result = await invitation_service.get_invitations(params=params)
    return result
 