"""
配置服务实现。
提供系统配置管理的业务逻辑实现。
"""

from typing import Optional, List, Dict, Any, Tuple
import logging

from sqlalchemy.ext.asyncio import AsyncSession
from redis.asyncio import Redis

from svc.core.services import BaseService
from svc.core.services.result import Result, ResultFactory
from svc.core.exceptions.error_codes import ErrorCode
from svc.apps.system.repositories.config import ConfigRepository, ConfigItem

class ConfigService(BaseService[ConfigItem, Result[Dict[str, Any]]]):
    """
    配置服务类，提供配置项管理功能
    
    属性:
        resource_type: 资源类型名称
        config_repo: 配置仓库实例
    """
    
    # 设置资源类型名称
    resource_type = "config"
    
    def __init__(
        self, 
        db: AsyncSession, 
        redis: Optional[Redis] = None,
        config_repo: Optional[ConfigRepository] = None
    ):
        """初始化配置服务
        
        Args:
            db: 数据库会话
            redis: Redis客户端（可选）
            config_repo: 配置仓库实例，不提供则创建新实例
        """
        super().__init__(db, redis)
        self.logger = logging.getLogger(__name__)
        self.config_repo = config_repo or ConfigRepository()
    
    async def get_config(self, config_id: int) -> Result[Dict[str, Any]]:
        """
        获取配置项
        
        Args:
            config_id: 配置项ID
            
        Returns:
            Result[Dict[str, Any]]: 配置项
        """
        try:
            config_item = await self.config_repo.get_by_id(self.db, config_id)
            if not config_item:
                return ResultFactory.error(
                    ErrorCode.NOT_FOUND, 
                    f"配置项(ID={config_id})不存在"
                )
                
            return ResultFactory.success(config_item.to_dict())
        except Exception as e:
            self.logger.error(f"获取配置项失败: {str(e)}", exc_info=True)
            return ResultFactory.error(ErrorCode.INTERNAL_ERROR, f"获取配置项失败: {str(e)}")
    
    async def get_configs(self, prefix: Optional[str] = None) -> Result[List[Dict[str, Any]]]:
        """
        获取配置项列表
        
        Args:
            prefix: 配置项键前缀（可选）
            
        Returns:
            Result[List[Dict[str, Any]]]: 配置项列表
        """
        try:
            config_items = await self.config_repo.get_by_prefix(self.db, prefix or "")
            return ResultFactory.success([item.to_dict() for item in config_items])
        except Exception as e:
            self.logger.error(f"获取配置项列表失败: {str(e)}", exc_info=True)
            return ResultFactory.error(ErrorCode.INTERNAL_ERROR, f"获取配置项列表失败: {str(e)}")
    
    async def create_config(self, data: Dict[str, Any]) -> Result[Dict[str, Any]]:
        """
        创建配置项
        
        Args:
            data: 配置项数据
            
        Returns:
            Result[Dict[str, Any]]: 创建的配置项
        """
        try:
            # 检查键是否已存在
            existing_items = await self.config_repo.get_by_prefix(self.db, data["key"])
            for item in existing_items:
                if item.key == data["key"]:
                    return ResultFactory.error(
                        ErrorCode.ALREADY_EXISTS, 
                        f"配置项键'{data['key']}'已存在"
                    )
            
            # 创建配置项
            config_item = await self.config_repo.create(self.db, data)
            return ResultFactory.success(config_item.to_dict())
        except Exception as e:
            self.logger.error(f"创建配置项失败: {str(e)}", exc_info=True)
            return ResultFactory.error(ErrorCode.INTERNAL_ERROR, f"创建配置项失败: {str(e)}")
    
    async def update_config(self, config_id: int, data: Dict[str, Any]) -> Result[Dict[str, Any]]:
        """
        更新配置项
        
        Args:
            config_id: 配置项ID
            data: 配置项数据
            
        Returns:
            Result[Dict[str, Any]]: 更新后的配置项
        """
        try:
            # 检查配置项是否存在
            existing_item = await self.config_repo.get_by_id(self.db, config_id)
            if not existing_item:
                return ResultFactory.error(
                    ErrorCode.NOT_FOUND, 
                    f"配置项(ID={config_id})不存在"
                )
            
            # 更新配置项
            updated_item = await self.config_repo.update(self.db, existing_item, data)
            return ResultFactory.success(updated_item.to_dict())
        except Exception as e:
            self.logger.error(f"更新配置项失败: {str(e)}", exc_info=True)
            return ResultFactory.error(ErrorCode.INTERNAL_ERROR, f"更新配置项失败: {str(e)}")
    
    async def delete_config(self, config_id: int) -> Result[Dict[str, Any]]:
        """
        删除配置项
        
        Args:
            config_id: 配置项ID
            
        Returns:
            Result[Dict[str, Any]]: 删除结果
        """
        try:
            # 检查配置项是否存在
            existing_item = await self.config_repo.get_by_id(self.db, config_id)
            if not existing_item:
                return ResultFactory.error(
                    ErrorCode.NOT_FOUND, 
                    f"配置项(ID={config_id})不存在"
                )
            
            # 删除配置项
            result = await self.config_repo.delete(self.db, config_id)
            if result:
                return ResultFactory.success({"id": config_id, "deleted": True})
            else:
                return ResultFactory.error(
                    ErrorCode.OPERATION_FAILED, 
                    f"删除配置项(ID={config_id})失败"
                )
        except Exception as e:
            self.logger.error(f"删除配置项失败: {str(e)}", exc_info=True)
            return ResultFactory.error(ErrorCode.INTERNAL_ERROR, f"删除配置项失败: {str(e)}")
    
    async def update_configs_by_prefix(
        self, 
        prefix: str, 
        new_value: str
    ) -> Result[Dict[str, Any]]:
        """
        根据前缀批量更新配置项
        
        Args:
            prefix: 配置项键前缀
            new_value: 新值
            
        Returns:
            Result[Dict[str, Any]]: 更新结果
        """
        try:
            if not prefix:
                return ResultFactory.error(
                    ErrorCode.INVALID_PARAMS, 
                    "前缀不能为空"
                )
            
            # 批量更新
            count, updated_items = await self.config_repo.update_by_prefix(
                self.db, prefix, new_value
            )
            
            if count == 0:
                return ResultFactory.error(
                    ErrorCode.NOT_FOUND, 
                    f"没有找到前缀为'{prefix}'的配置项"
                )
            
            result = {
                "updated_count": count,
                "items": [item.to_dict() for item in updated_items]
            }
            return ResultFactory.success(result)
        except Exception as e:
            self.logger.error(f"批量更新配置项失败: {str(e)}", exc_info=True)
            return ResultFactory.error(ErrorCode.INTERNAL_ERROR, f"批量更新配置项失败: {str(e)}") 