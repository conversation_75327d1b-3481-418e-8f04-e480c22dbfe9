"""
系统监控服务实现。
专门负责系统健康检查、性能监控、系统信息收集等功能。
"""

import logging
import platform
import time
from datetime import datetime, timezone
from typing import Any, Dict, Optional

import psutil
from redis.asyncio import Redis
from sqlalchemy import text
from sqlalchemy.ext.asyncio import AsyncSession

from svc.core.exceptions.error_codes import ErrorCode
from svc.core.services import BaseService
from svc.core.services.result import Result, ResultFactory


class SystemMonitorService(BaseService[None, Result[Dict[str, Any]]]):
    """
    系统监控服务类，专门处理系统监控相关功能
    
    主要功能：
    - 系统健康检查
    - 性能指标收集
    - 服务状态监控
    - 系统信息获取
    """
    
    resource_type = "system_monitor"
    
    def __init__(self, db: AsyncSession, redis: Optional[Redis] = None):
        """初始化系统监控服务
        
        Args:
            db: 数据库会话
            redis: Redis客户端（可选）
        """
        super().__init__(db, redis)
        self.logger = logging.getLogger(__name__)
        self.start_time = time.time()
    
    async def get_health_status(self) -> Result[Dict[str, Any]]:
        """
        获取系统健康状态
        
        Returns:
            Result[Dict[str, Any]]: 健康状态信息
        """
        try:
            # 检查各个服务组件的状态
            services_status = {}
            overall_status = "healthy"
            
            # 检查数据库连接
            db_status = await self._check_database_health()
            services_status["database"] = db_status
            if db_status != "healthy":
                overall_status = "unhealthy"
            
            # 检查Redis连接
            redis_status = await self._check_redis_health()
            services_status["redis"] = redis_status
            if redis_status == "unhealthy":
                overall_status = "unhealthy"
            elif redis_status == "warning" and overall_status == "healthy":
                overall_status = "warning"
            
            # 检查系统资源
            resource_status = await self._check_system_resources()
            services_status["system_resources"] = resource_status["status"]
            if resource_status["status"] == "unhealthy":
                overall_status = "unhealthy"
            elif resource_status["status"] == "warning" and overall_status == "healthy":
                overall_status = "warning"
            
            result = {
                "status": overall_status,
                "timestamp": datetime.now(timezone.utc).isoformat(),
                "services": services_status,
                "uptime": self._get_uptime(),
                "details": {
                    "database": await self._get_database_details(),
                    "redis": await self._get_redis_details(),
                    "system": resource_status
                }
            }
            
            return ResultFactory.success(result)
        except Exception as e:
            self.logger.error(f"健康检查失败: {str(e)}", exc_info=True)
            return ResultFactory.error(ErrorCode.INTERNAL_ERROR, f"健康检查失败: {str(e)}")
    
    async def get_system_info(self) -> Result[Dict[str, Any]]:
        """
        获取系统信息
        
        Returns:
            Result[Dict[str, Any]]: 系统信息
        """
        try:
            info = {
                "application": {
                    "name": "AI Tools Service",
                    "version": "0.1.0",
                    "environment": "development",  # 实际应用中应从配置获取
                    "uptime": self._get_uptime()
                },
                "system": {
                    "platform": platform.platform(),
                    "python_version": platform.python_version(),
                    "architecture": platform.architecture()[0],
                    "processor": platform.processor(),
                    "hostname": platform.node()
                },
                "resources": await self._get_system_resources(),
                "timestamp": datetime.now(timezone.utc).isoformat()
            }
            
            return ResultFactory.success(info)
        except Exception as e:
            self.logger.error(f"获取系统信息失败: {str(e)}", exc_info=True)
            return ResultFactory.error(ErrorCode.INTERNAL_ERROR, f"获取系统信息失败: {str(e)}")
    
    async def get_performance_metrics(self) -> Result[Dict[str, Any]]:
        """
        获取性能指标
        
        Returns:
            Result[Dict[str, Any]]: 性能指标
        """
        try:
            metrics = {
                "cpu": {
                    "usage_percent": psutil.cpu_percent(interval=1),
                    "count": psutil.cpu_count(),
                    "load_average": psutil.getloadavg() if hasattr(psutil, 'getloadavg') else None
                },
                "memory": {
                    "total": psutil.virtual_memory().total,
                    "available": psutil.virtual_memory().available,
                    "used": psutil.virtual_memory().used,
                    "percent": psutil.virtual_memory().percent
                },
                "disk": {
                    "total": psutil.disk_usage('/').total,
                    "used": psutil.disk_usage('/').used,
                    "free": psutil.disk_usage('/').free,
                    "percent": psutil.disk_usage('/').percent
                },
                "network": await self._get_network_stats(),
                "database": await self._get_database_metrics(),
                "redis": await self._get_redis_metrics(),
                "timestamp": datetime.now(timezone.utc).isoformat()
            }
            
            return ResultFactory.success(metrics)
        except Exception as e:
            self.logger.error(f"获取性能指标失败: {str(e)}", exc_info=True)
            return ResultFactory.error(ErrorCode.INTERNAL_ERROR, f"获取性能指标失败: {str(e)}")
    
    # 私有方法
    
    async def _check_database_health(self) -> str:
        """检查数据库健康状态"""
        try:
            # 执行简单查询测试连接
            result = await self.db.execute(text("SELECT 1"))
            await result.fetchone()
            return "healthy"
        except Exception as e:
            self.logger.error(f"数据库健康检查失败: {str(e)}")
            return "unhealthy"
    
    async def _check_redis_health(self) -> str:
        """检查Redis健康状态"""
        if not self.redis:
            return "not_configured"
        
        try:
            await self.redis.ping()
            return "healthy"
        except Exception as e:
            self.logger.error(f"Redis健康检查失败: {str(e)}")
            return "unhealthy"
    
    async def _check_system_resources(self) -> Dict[str, Any]:
        """检查系统资源状态"""
        try:
            cpu_percent = psutil.cpu_percent(interval=1)
            memory_percent = psutil.virtual_memory().percent
            disk_percent = psutil.disk_usage('/').percent
            
            # 根据资源使用率判断状态
            status = "healthy"
            if cpu_percent > 90 or memory_percent > 90 or disk_percent > 90:
                status = "unhealthy"
            elif cpu_percent > 80 or memory_percent > 80 or disk_percent > 80:
                status = "warning"
            
            return {
                "status": status,
                "cpu_percent": cpu_percent,
                "memory_percent": memory_percent,
                "disk_percent": disk_percent
            }
        except Exception as e:
            self.logger.error(f"系统资源检查失败: {str(e)}")
            return {"status": "unknown", "error": str(e)}
    
    def _get_uptime(self) -> str:
        """获取应用运行时间"""
        uptime_seconds = int(time.time() - self.start_time)
        days = uptime_seconds // 86400
        hours = (uptime_seconds % 86400) // 3600
        minutes = (uptime_seconds % 3600) // 60
        seconds = uptime_seconds % 60
        
        if days > 0:
            return f"{days}d {hours}h {minutes}m {seconds}s"
        elif hours > 0:
            return f"{hours}h {minutes}m {seconds}s"
        elif minutes > 0:
            return f"{minutes}m {seconds}s"
        else:
            return f"{seconds}s"
    
    async def _get_system_resources(self) -> Dict[str, Any]:
        """获取系统资源信息"""
        try:
            return {
                "cpu": {
                    "usage_percent": psutil.cpu_percent(),
                    "count": psutil.cpu_count()
                },
                "memory": {
                    "total_gb": round(psutil.virtual_memory().total / (1024**3), 2),
                    "used_gb": round(psutil.virtual_memory().used / (1024**3), 2),
                    "percent": psutil.virtual_memory().percent
                },
                "disk": {
                    "total_gb": round(psutil.disk_usage('/').total / (1024**3), 2),
                    "used_gb": round(psutil.disk_usage('/').used / (1024**3), 2),
                    "percent": psutil.disk_usage('/').percent
                }
            }
        except Exception as e:
            self.logger.error(f"获取系统资源信息失败: {str(e)}")
            return {"error": str(e)}
    
    async def _get_database_details(self) -> Dict[str, Any]:
        """获取数据库详细信息"""
        try:
            # 获取数据库版本和连接信息
            result = await self.db.execute(text("SELECT version()"))
            version = await result.fetchone()
            
            return {
                "status": "connected",
                "version": version[0] if version else "unknown",
                "pool_size": getattr(self.db.bind.pool, 'size', 'unknown'),
                "checked_in": getattr(self.db.bind.pool, 'checkedin', 'unknown')
            }
        except Exception as e:
            return {"status": "error", "error": str(e)}
    
    async def _get_redis_details(self) -> Dict[str, Any]:
        """获取Redis详细信息"""
        if not self.redis:
            return {"status": "not_configured"}
        
        try:
            info = await self.redis.info()
            return {
                "status": "connected",
                "version": info.get("redis_version", "unknown"),
                "used_memory": info.get("used_memory_human", "unknown"),
                "connected_clients": info.get("connected_clients", "unknown")
            }
        except Exception as e:
            return {"status": "error", "error": str(e)}
    
    async def _get_network_stats(self) -> Dict[str, Any]:
        """获取网络统计信息"""
        try:
            net_io = psutil.net_io_counters()
            return {
                "bytes_sent": net_io.bytes_sent,
                "bytes_recv": net_io.bytes_recv,
                "packets_sent": net_io.packets_sent,
                "packets_recv": net_io.packets_recv
            }
        except Exception as e:
            return {"error": str(e)}
    
    async def _get_database_metrics(self) -> Dict[str, Any]:
        """获取数据库性能指标"""
        try:
            # 这里可以添加更多数据库特定的性能指标
            return {
                "connection_status": "active",
                "pool_status": "healthy"
            }
        except Exception as e:
            return {"error": str(e)}
    
    async def _get_redis_metrics(self) -> Dict[str, Any]:
        """获取Redis性能指标"""
        if not self.redis:
            return {"status": "not_configured"}
        
        try:
            info = await self.redis.info()
            return {
                "keyspace_hits": info.get("keyspace_hits", 0),
                "keyspace_misses": info.get("keyspace_misses", 0),
                "used_memory": info.get("used_memory", 0),
                "connected_clients": info.get("connected_clients", 0)
            }
        except Exception as e:
            return {"error": str(e)}
