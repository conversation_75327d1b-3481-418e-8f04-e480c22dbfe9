"""
审计日志API路由。
提供查询系统审计日志的API端点。
"""

from datetime import datetime
from typing import Any, Dict, List, Optional

from fastapi import APIRouter, Depends, HTTPException, Path, Query, Request

from svc.apps.system.dependencies import get_system_service
from svc.apps.system.schemas.audit_log import (AuditLogListResponse,
                                               AuditLogResponse)
from svc.apps.system.services.system import SystemService
from svc.core.services.result import Result

# 创建路由器
router = APIRouter(
    prefix="/audit-logs",
    tags=["audit-logs"]
)

@router.get(
    "", 
    response_model=AuditLogListResponse,
    summary="查询审计日志列表",
    description="获取系统审计日志列表，支持按各种条件过滤和分页"
)
async def search_audit_logs(
    action: Optional[str] = Query(None, description="操作类型"),
    resource_type: Optional[str] = Query(None, description="资源类型"),
    resource_id: Optional[str] = Query(None, description="资源ID"),
    user_id: Optional[int] = Query(None, description="用户ID"),
    username: Optional[str] = Query(None, description="用户名"),
    status: Optional[str] = Query(None, description="操作结果"),
    start_date: Optional[datetime] = Query(None, description="开始日期"),
    end_date: Optional[datetime] = Query(None, description="结束日期"),
    page: int = Query(1, ge=1, description="页码"),
    page_size: int = Query(20, ge=1, le=100, description="每页条数"),
    system_service: SystemService = Depends(get_system_service),
) -> AuditLogListResponse:
    """
    查询审计日志列表
    
    Args:
        request: 请求对象
        action: 操作类型
        resource_type: 资源类型
        resource_id: 资源ID
        user_id: 用户ID
        username: 用户名
        status: 操作结果
        start_date: 开始日期
        end_date: 结束日期
        page: 页码
        page_size: 每页条数

    Returns:
        AuditLogListResponse: 审计日志列表和分页信息
    """
    result: Result[Dict[str, Any]] = await system_service.search_audit_logs(
        action=action,
        resource_type=resource_type,
        resource_id=resource_id,
        user_id=user_id,
        username=username,
        status=status,
        start_date=start_date,
        end_date=end_date,
        page=page,
        page_size=page_size
    )
    
    if result.success:
        # 构造响应数据
        return AuditLogListResponse(
            items=result.data["items"],
            total=result.data["total"],
            page=result.data["page"],
            page_size=result.data["page_size"]
        )
    else:
        # 处理错误
        raise HTTPException(
            status_code=result.status_code or 500,
            detail=result.message or "查询审计日志失败"
        )

@router.get(
    "/{log_id}", 
    response_model=AuditLogResponse,
    summary="获取审计日志详情",
    description="获取单个审计日志的详细信息"
)
async def get_audit_log(
    log_id: int = Path(..., description="审计日志ID"),
    system_service: SystemService = Depends(get_system_service),
) -> AuditLogResponse:
    """
    获取审计日志详情
    
    Args:
        log_id: 审计日志ID
        
    Returns:
        AuditLogResponse: 审计日志详情
    """
    result = await system_service.get_audit_log(log_id)
    
    if result.success:
        return AuditLogResponse(**result.data)
    else:
        raise HTTPException(
            status_code=result.status_code or 500,
            detail=result.message or f"获取审计日志(ID={log_id})失败"
        )

@router.get(
    "/latest", 
    response_model=List[AuditLogResponse],
    summary="获取最新审计日志",
    description="获取系统中最新的几条审计日志记录"
)
async def get_latest_audit_logs(
    limit: int = Query(10, ge=1, le=100, description="返回数量限制"),
    system_service: SystemService = Depends(get_system_service),
) -> List[AuditLogResponse]:
    """
    获取最新审计日志
    
    Args:
        limit: 返回数量限制
        
    Returns:
        List[AuditLogResponse]: 审计日志列表
    """
    result = await system_service.get_latest_audit_logs(limit)
    
    if result.success:
        return [AuditLogResponse(**log) for log in result.data]
    else:
        raise HTTPException(
            status_code=result.status_code or 500,
            detail=result.message or "获取最新审计日志失败"
        )

@router.get(
    "/resources/{resource_type}/{resource_id}", 
    response_model=List[AuditLogResponse],
    summary="获取资源审计日志",
    description="获取特定资源的所有审计日志记录"
)
async def get_resource_audit_logs(
    resource_type: str = Path(..., description="资源类型"),
    resource_id: str = Path(..., description="资源ID"),
    system_service: SystemService = Depends(get_system_service),
) -> List[AuditLogResponse]:
    """
    获取资源审计日志
    
    Args:
        resource_type: 资源类型
        resource_id: 资源ID
        
    Returns:
        List[AuditLogResponse]: 审计日志列表
    """
    result = await system_service.get_resource_audit_logs(resource_type, resource_id)
    
    if result.success:
        return [AuditLogResponse(**log) for log in result.data]
    else:
        raise HTTPException(
            status_code=result.status_code or 500,
            detail=result.message or f"获取资源({resource_type}/{resource_id})审计日志失败"
        )


@router.get(
    "/users/{user_id}",
    response_model=List[AuditLogResponse],
    summary="获取用户审计日志",
    description="获取特定用户的所有审计日志记录"
)
async def get_user_audit_logs(
    user_id: int = Path(..., description="用户ID"),
    limit: int = Query(50, ge=1, le=200, description="返回数量限制"),
    system_service: SystemService = Depends(get_system_service),
) -> List[AuditLogResponse]:
    """
    获取用户审计日志

    Args:
        user_id: 用户ID
        limit: 返回数量限制

    Returns:
        List[AuditLogResponse]: 审计日志列表
    """
    result = await system_service.get_user_audit_logs(user_id, limit)

    if result.success:
        return [AuditLogResponse(**log) for log in result.data]
    else:
        raise HTTPException(
            status_code=result.status_code or 500,
            detail=result.message or f"获取用户({user_id})审计日志失败"
        )


@router.get(
    "/statistics",
    response_model=Result[Dict[str, Any]],
    summary="获取审计日志统计",
    description="获取审计日志的统计信息，包括操作类型分布、用户活跃度等"
)
async def get_audit_statistics(
    start_date: Optional[datetime] = Query(None, description="开始日期"),
    end_date: Optional[datetime] = Query(None, description="结束日期"),
    system_service: SystemService = Depends(get_system_service),
) -> Result[Dict[str, Any]]:
    """
    获取审计日志统计信息

    Args:
        start_date: 开始日期，默认为最近30天
        end_date: 结束日期，默认为当前时间

    Returns:
        Result[Dict[str, Any]]: 统计信息
    """
    return await system_service.get_audit_statistics(start_date, end_date)