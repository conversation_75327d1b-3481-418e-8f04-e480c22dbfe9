"""
商品变体API路由
包含商品变体的创建、查询、更新和删除功能
"""
from typing import Any

from fastapi import APIRouter, Depends, Query, Path, status

from svc.core.services.result import Result
from svc.apps.auth.dependencies import (
    get_current_active_user,
    has_permission,
    resource_permission
)
from svc.apps.auth.models.user import User
from svc.apps.products.schemas.product_variant import (
    ProductVariantCreate,
    ProductVariantUpdate,
    ProductVariantListResponse
)
from svc.apps.products.services.product_variant import ProductVariantService
from svc.apps.products.dependencies import get_product_variant_service
from svc.core.exceptions import (
    handle_route_errors,
    PRODUCT_VARIANT_ERROR_MAPPING
)

# 创建路由器
router = APIRouter(
    tags=["商品变体管理"]
)

# === 管理端路由 (Admin Routes) ===

@router.get("/admin/product/{product_id}/variants", response_model=Result[ProductVariantListResponse])
@handle_route_errors(PRODUCT_VARIANT_ERROR_MAPPING)
async def admin_list_product_variants(
    product_id: int = Path(..., description="商品ID"),
    page_num: int = Query(1, ge=1, description="页码"),
    page_size: int = Query(10, ge=1, le=1000, description="每页数量"),
    variant_service: ProductVariantService = Depends(get_product_variant_service),
    current_user: User = Depends(get_current_active_user),
    _: bool = Depends(lambda: has_permission("product_variant:read"))
) -> Result[ProductVariantListResponse]:
    """获取商品变体列表 (管理端)"""
    result = await variant_service.get_variants_by_product_id(
        product_id=product_id,
        page_num=page_num,
        page_size=page_size
    )
    return result

@router.get("/admin/{variant_id}", response_model=Result)
@handle_route_errors(PRODUCT_VARIANT_ERROR_MAPPING)
async def admin_get_variant_details(
    variant_id: int = Path(..., description="变体ID"),
    variant_service: ProductVariantService = Depends(get_product_variant_service),
    current_user: User = Depends(get_current_active_user),
    _: Any = Depends(resource_permission("product_variant", "read")),
) -> Result:
    """获取变体详情 (管理端)"""
    result = await variant_service.get_variant(variant_id=variant_id)
    return result

@router.post("/admin/", response_model=Result, status_code=status.HTTP_201_CREATED)
@handle_route_errors(PRODUCT_VARIANT_ERROR_MAPPING)
async def admin_create_variant(
    variant_data: ProductVariantCreate,
    variant_service: ProductVariantService = Depends(get_product_variant_service),
    current_user: User = Depends(get_current_active_user),
    _: bool = Depends(lambda: has_permission("product_variant:create")),
) -> Result:
    """创建变体 (管理端)"""
    result = await variant_service.create_variant(params=variant_data)
    return result

@router.put("/admin/{variant_id}", response_model=Result)
@handle_route_errors(PRODUCT_VARIANT_ERROR_MAPPING)
async def admin_update_variant(
    variant_in: ProductVariantUpdate,
    variant_id: int = Path(..., description="变体ID"),
    variant_service: ProductVariantService = Depends(get_product_variant_service),
    current_user: User = Depends(get_current_active_user),
    _: Any = Depends(resource_permission("product_variant", "update")),
) -> Result:
    """更新变体 (管理端)"""
    result = await variant_service.update_variant(variant_id=variant_id, params=variant_in)
    return result

# === 客户端路由 (Client Routes) ===

@router.get("/product/{product_id}/variants", response_model=Result[ProductVariantListResponse])
@handle_route_errors(PRODUCT_VARIANT_ERROR_MAPPING)
async def list_product_variants(
    product_id: int = Path(..., description="商品ID"),
    page_num: int = Query(1, ge=1, description="页码"),
    page_size: int = Query(10, ge=1, le=1000, description="每页数量"),
    variant_service: ProductVariantService = Depends(get_product_variant_service),
) -> Result[ProductVariantListResponse]:
    """获取商品变体列表 (客户端)"""
    result = await variant_service.get_variants_by_product_id(
        product_id=product_id,
        page_num=page_num,
        page_size=page_size
    )
    return result

@router.get("/{variant_id}", response_model=Result)
@handle_route_errors(PRODUCT_VARIANT_ERROR_MAPPING)
async def get_variant_details(
    variant_id: int = Path(..., description="变体ID"),
    variant_service: ProductVariantService = Depends(get_product_variant_service),
) -> Result:
    """获取变体详情 (客户端)"""
    result = await variant_service.get_variant(variant_id=variant_id)
    return result
