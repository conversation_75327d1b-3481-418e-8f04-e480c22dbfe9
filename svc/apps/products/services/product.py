from typing import Optional, Dict, Any

from sqlalchemy.ext.asyncio import AsyncSession
from redis.asyncio import Redis

from svc.apps.products.repositories.product import ProductRepository
from svc.core.exceptions.error_codes import ErrorCode
from svc.core.services import BaseService
from svc.core.services.result import Result
from svc.apps.products.models.product import Product, ProductStatus
from svc.apps.products.schemas.product import (
    ProductResponse,
    ProductCreate,
    ProductUpdate,
    ProductListResponse,
    GetProductsParams
)
from svc.core.utils.datetime_utils import get_utc_now_without_tzinfo
from fastapi_events.dispatcher import dispatch

# 缓存配置
CACHE_TTL = 3600  # 默认缓存过期时间（1小时）
CACHE_TTL_SHORT = 300  # 短期缓存过期时间（5分钟）
CACHE_TTL_LONG = 86400  # 长期缓存过期时间（24小时）

class ProductService(BaseService[Product, Result[ProductResponse]]):
    """商品服务类，提供商品的创建、查询和管理功能
    
    该服务类负责：
    1. 商品的创建和管理
    2. 商品状态更新
    3. 商品数据统计
    4. 商品库存管理
    
    服务类依赖ProductRepository进行数据访问，
    实现业务逻辑与数据访问的分离。
    """
    
    # 设置资源类型名称
    resource_type = "product"
    
    def __init__(
        self, 
        redis: Optional[Redis] = None, 
        product_repo: Optional[ProductRepository] = None
    ):
        """初始化商品服务
        
        Args:
            redis: Redis客户端，用于缓存和分布式锁
            product_repo: 商品仓库实例，不提供则创建新实例
        """
        super().__init__(redis)
        self.product_repo = product_repo
    
    async def get_resource_by_id(self, product_id: int) -> Optional[Product]:
        """获取指定ID的商品资源
        
        Args:
            product_id: 商品ID
            
        Returns:
            Optional[Product]: 商品对象，不存在时返回None
        """
        return await self.product_repo.get_by_id(product_id)
    
    async def get_product(self, product_id: int) -> Result[ProductResponse]:
        """获取商品信息
        
        Args:
            product_id: 商品ID
            
        Returns:
            Result[ProductResponse]: 结果对象
        """
        try:
            self.logger.info(f"获取商品: id={product_id}")
            
            # 先尝试从缓存获取
            if self.redis:
                try:
                    cache_key = self._get_resource_cache_key(product_id)
                    cached_product = await self.get_cached_resource(
                        cache_key,
                        lambda data: ProductResponse.model_validate(data)
                    )
                    if cached_product:
                        self.logger.debug(f"从缓存获取到商品: id={product_id}")
                        return self.create_success_result(cached_product)
                except Exception as e:
                    self.logger.warning(f"从缓存获取商品失败: id={product_id}, 错误={str(e)}")
            
            # 查询数据库
            product = await self.get_resource_by_id(product_id)
            if not product:
                self.logger.warning(f"商品不存在: id={product_id}")
                return self.resource_not_found_result(product_id)
            
            # 构建商品响应
            product_response = ProductResponse.model_validate(product.to_dict())
            
            # 缓存商品
            await self._cache_product(product_id, product_response)
            
            # 增加浏览次数
            await self.product_repo.increment_view_count(product_id)
            
            return self.create_success_result(product_response)
        except Exception as e:
            self.logger.error(f"获取商品失败: id={product_id}, 错误={str(e)}", exc_info=True)
            return self.create_error_result(
                error_code=ErrorCode.NOT_FOUND,
                error_message=f"获取商品失败: {str(e)}"
            )
    
    async def get_products(self, params: GetProductsParams) -> Result[ProductListResponse]:
        """获取商品列表
        
        Args:
            params: 查询参数对象，包含分页和过滤条件
            
        Returns:
            Result[ProductListResponse]: 包含分页信息的商品列表结果
        """
        try:
            # Extract parameters from the params object
            page_num = params.page_num
            page_size = params.page_size
            skip = (page_num - 1) * page_size
            self.logger.info(f"获取商品列表: page={page_num}, size={page_size}")
            
            # 使用仓库类获取商品列表和总数
            products, total = await self.product_repo.get_products(
                skip=skip,
                limit=page_size,
                status=params.status,
                category_id=params.category_id,
                is_featured=params.is_featured,
                search_term=params.search_term,
                min_price=params.min_price,
                max_price=params.max_price,
                order_by=params.order_by,
                order_desc=params.order_desc
            )
            
            # 构建商品响应列表
            product_responses = []
            for product in products:
                product_response = ProductResponse.model_validate(product.to_dict())
                product_responses.append(product_response)
                
            # 计算总页数
            total_pages = (total + page_size - 1) // page_size if page_size > 0 else 0
            
            # 构建分页响应
            paginated_response = ProductListResponse(
                items=product_responses,
                total=total,
                page=page_num,
                size=page_size,
                pages=total_pages
            )
            
            return self.create_success_result(paginated_response)
        except Exception as e:
            self.logger.error(f"获取商品列表失败: 错误={str(e)}", exc_info=True)
            return self.create_error_result(
                error_code=ErrorCode.OPERATION_FAILED,
                error_message=f"获取商品列表失败: {str(e)}"
            )
    
    async def create_product(self, params: ProductCreate) -> Result[ProductResponse]:
        """创建商品
        
        Args:
            params: 商品创建参数
            
        Returns:
            Result[ProductResponse]: 结果对象
        """
        try:
            self.logger.info(f"创建商品: name={params.name}, sku={params.sku}")
            
            # 检查SKU是否已存在
            existing_product = await self.product_repo.get_by_sku(params.sku)
            if existing_product:
                self.logger.warning(f"商品SKU已存在: {params.sku}")
                return self.create_error_result(
                    error_code=ErrorCode.ALREADY_EXISTS,
                    error_message=f"商品SKU {params.sku} 已被使用"
                )
            
            # 创建商品
            product = await self.product_repo.create(**params.model_dump())
            
            # 构建商品响应
            product_response = ProductResponse.model_validate(product.to_dict())
            
            # 缓存商品
            await self._cache_product(product.id, product_response)
            
            # 触发商品创建事件
            event_data = product_response.model_dump()
            dispatch("products:product:created", payload=event_data)
            
            self.logger.info(f"商品创建成功: id={product.id}, name={product.name}")
            return self.create_success_result(product_response)
        except Exception as e:
            self.logger.error(f"创建商品失败: 错误={str(e)}", exc_info=True)
            return self.create_error_result(
                error_code=ErrorCode.OPERATION_FAILED,
                error_message=f"创建商品失败: {str(e)}"
            )
    
    async def update_product(self, product_id: int, params: ProductUpdate) -> Result[ProductResponse]:
        """更新商品
        
        Args:
            product_id: 商品ID
            params: 商品更新参数
            
        Returns:
            Result[ProductResponse]: 结果对象
        """
        try:
            self.logger.info(f"更新商品: id={product_id}")
            
            # 获取商品
            product = await self.get_resource_by_id(product_id)
            if not product:
                self.logger.warning(f"商品不存在: {product_id}")
                return self.resource_not_found_result(product_id)
            
            # 如果更新SKU，检查是否已存在
            if params.sku is not None and params.sku != product.sku:
                existing_product = await self.product_repo.get_by_sku(params.sku)
                if existing_product and existing_product.id != product.id:
                    self.logger.warning(f"商品SKU已存在: {params.sku}")
                    return self.create_error_result(
                        error_code=ErrorCode.ALREADY_EXISTS,
                        error_message=f"商品SKU {params.sku} 已被使用"
                    )
            
            # 更新商品
            update_data = params.model_dump(exclude_unset=True)
            product = await self.product_repo.update(product, data=update_data)
            
            # 构建商品响应
            product_response = ProductResponse.model_validate(product.to_dict())
            
            # 缓存商品
            await self._cache_product(product.id, product_response)
            
            # 触发商品更新事件
            event_data = product_response.model_dump()
            event_data["updated_fields"] = list(update_data.keys())
            dispatch("products:product:updated", payload=event_data)
            
            self.logger.info(f"商品更新成功: id={product.id}, name={product.name}")
            return self.create_success_result(product_response)
        except Exception as e:
            self.logger.error(f"更新商品失败: id={product_id}, 错误={str(e)}", exc_info=True)
            return self.create_error_result(
                error_code=ErrorCode.INTERNAL_ERROR,
                error_message=f"更新商品失败: {str(e)}"
            )
    
    async def _cache_product(self, product_id: int, response: ProductResponse) -> None:
        """缓存商品信息
        
        Args:
            product_id: 商品ID
            response: 商品响应对象
        """
        if not self.redis:
            self.logger.debug(f"Redis未配置，跳过缓存商品: product_id={product_id}")
            return
            
        try:
            # 缓存商品数据
            key = self._get_resource_cache_key(product_id)
            await self.cache_resource(key, response, CACHE_TTL)
            self.logger.debug(f"商品缓存成功: key={key}")
        except Exception as e:
            # 缓存失败不应影响主要业务逻辑
            self.logger.warning(f"缓存商品失败: product_id={product_id}, 错误={str(e)}")
