"""
配置项模型。
提供系统配置项的数据模型定义。
"""

from sqlalchemy import Column, Integer, String, Text, DateTime, func

from svc.core.database.session import Base

class ConfigItem(Base):
    """
    配置项模型
    
    存储系统配置项信息
    """
    __tablename__ = "system_config_items"
    
    id = Column(Integer, primary_key=True, index=True, autoincrement=True)
    key = Column(String(100), nullable=False, unique=True, index=True, comment="配置键")
    value = Column(Text, nullable=False, comment="配置值")
    description = Column(String(255), nullable=True, comment="配置描述")
    created_at = Column(DateTime, default=func.now(), nullable=False, comment="创建时间")
    updated_at = Column(DateTime, default=func.now(), onupdate=func.now(), nullable=False, comment="更新时间")
    
    def to_dict(self):
        """转换为字典"""
        return {
            "id": self.id,
            "key": self.key,
            "value": self.value,
            "description": self.description,
            "created_at": self.created_at.isoformat() if self.created_at else None,
            "updated_at": self.updated_at.isoformat() if self.updated_at else None
        } 