"""
健康检查路由模块。
提供系统健康状态检查的API接口。
"""

from typing import Dict, Any, List
from pydantic import BaseModel, Field

from fastapi import APIRouter, Depends, Query
from sqlalchemy.ext.asyncio import AsyncSession

from svc.core.services.result import Result
from svc.apps.system.services.system import SystemService
from svc.apps.system.dependencies import get_system_service

# 创建路由器
router = APIRouter(prefix="/health", tags=["健康检查"])

# 定义响应模型
class HealthCheckItem(BaseModel):
    """健康检查项目模型"""
    name: str = Field(..., description="检查项名称")
    status: str = Field(..., description="状态：healthy, unhealthy, warning, unknown")
    details: Dict[str, Any] = Field(default={}, description="详细信息")

class HealthCheckResponse(BaseModel):
    """健康检查响应模型"""
    status: str = Field(..., description="整体状态：healthy, unhealthy, warning, unknown")
    timestamp: str = Field(..., description="检查时间")
    services: Dict[str, str] = Field(..., description="服务状态")
    details: List[HealthCheckItem] = Field(default=[], description="详细检查项")

# === 路由处理函数 ===

@router.get("", response_model=Result[Dict[str, Any]], summary="获取健康状态")
async def get_health_status(
    system_service: SystemService = Depends(get_system_service)
):
    """
    获取系统健康状态
    
    返回系统的健康状态信息，包括各个服务组件的状态。
    
    - **status**: 整体健康状态 (healthy, unhealthy, warning, unknown)
    - **services**: 各服务组件状态
    - **timestamp**: 检查时间
    """
    return await system_service.get_health_status()

@router.get("/detailed", response_model=Result[Dict[str, Any]], summary="获取详细健康状态")
async def get_detailed_health_status(
    check_db: bool = Query(True, description="是否检查数据库"),
    check_redis: bool = Query(True, description="是否检查Redis"),
    check_storage: bool = Query(False, description="是否检查存储"),
    system_service: SystemService = Depends(get_system_service)
):
    """
    获取详细的系统健康状态
    
    返回系统的详细健康状态信息，包括各个服务组件的状态和详细指标。
    可以通过查询参数控制需要检查的组件。
    
    - **status**: 整体健康状态 (healthy, unhealthy, warning, unknown)
    - **services**: 各服务组件状态
    - **details**: 详细检查项信息
    - **timestamp**: 检查时间
    """
    # 此处可以扩展SystemService以提供更详细的健康检查
    # 目前调用基本健康检查
    return await system_service.get_health_status()

@router.get("/liveness", status_code=204, summary="存活检查")
async def liveness_check():
    """
    简单的存活检查
    
    用于Kubernetes等容器编排平台的liveness probe。
    如果服务正在运行，返回204 No Content。
    """
    # 简单返回，不需要任何检查
    return None

@router.get("/readiness", response_model=Result[Dict[str, Any]], summary="就绪检查")
async def readiness_check(
    system_service: SystemService = Depends(get_system_service)
):
    """
    就绪状态检查
    
    用于Kubernetes等容器编排平台的readiness probe。
    检查系统是否已准备好处理请求，包括数据库连接等依赖服务是否正常。
    """
    return await system_service.get_health_status() 