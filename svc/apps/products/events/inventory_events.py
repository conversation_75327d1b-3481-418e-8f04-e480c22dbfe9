"""
库存事件处理器

该模块处理库存相关的事件，包括：
- 库存创建事件
- 库存调整事件
- 库存预留事件
- 库存释放事件
"""

import logging
from typing import Dict, Any

from fastapi_events.handlers.local import local_handler
from fastapi_events.typing import Event

from svc.core.database.session_utils import get_session_for_script
from svc.apps.products.repositories.product import ProductRepository
from svc.apps.products.repositories.product_variant import ProductVariantRepository

logger = logging.getLogger(__name__)

class InventoryEventHandler:
    """库存事件处理器类"""
    
    @staticmethod
    @local_handler.register(event_name="products:inventory:created")
    async def handle_inventory_created(event: Event):
        """处理库存创建事件
        
        当库存记录被创建时触发，执行以下操作：
        1. 更新商品/变体的库存数量
        2. 记录日志
        3. 发送通知
        
        Args:
            event: 事件对象，包含库存数据
        """
        try:
            logger.info(f"处理库存创建事件: {event}")
            
            inventory_data = event[1]  # 获取事件数据
            inventory_id = inventory_data.get("id")
            product_id = inventory_data.get("product_id")
            variant_id = inventory_data.get("variant_id")
            quantity = inventory_data.get("quantity")
            
            logger.info(f"库存已创建: ID={inventory_id}, 商品ID={product_id}, 变体ID={variant_id}, 数量={quantity}")
            
            # 更新商品或变体的库存数量
            async with get_session_for_script() as db:
                if variant_id:
                    # 更新变体库存
                    variant_repo = ProductVariantRepository(db)
                    await variant_repo.update_stock_quantity(variant_id, quantity)
                    logger.info(f"已更新变体库存: variant_id={variant_id}, quantity={quantity}")
                else:
                    # 更新商品库存
                    product_repo = ProductRepository(db)
                    await product_repo.update_stock_quantity(product_id, quantity)
                    logger.info(f"已更新商品库存: product_id={product_id}, quantity={quantity}")
                
                await db.commit()
            
        except Exception as e:
            logger.error(f"处理库存创建事件失败: {str(e)}", exc_info=True)
    
    @staticmethod
    @local_handler.register(event_name="products:inventory:adjusted")
    async def handle_inventory_adjusted(event: Event):
        """处理库存调整事件
        
        当库存数量被调整时触发，执行以下操作：
        1. 更新商品/变体的库存数量
        2. 记录调整日志
        3. 发送库存警告通知
        
        Args:
            event: 事件对象，包含库存调整数据
        """
        try:
            logger.info(f"处理库存调整事件: {event}")
            
            inventory_data = event[1]  # 获取事件数据
            inventory_id = inventory_data.get("id")
            product_id = inventory_data.get("product_id")
            variant_id = inventory_data.get("variant_id")
            adjustment_type = inventory_data.get("adjustment_type")
            adjustment_quantity = inventory_data.get("adjustment_quantity")
            reason = inventory_data.get("reason")
            
            logger.info(f"库存已调整: ID={inventory_id}, 类型={adjustment_type}, 数量={adjustment_quantity}, 原因={reason}")
            
            # 计算实际调整量
            quantity_change = adjustment_quantity if adjustment_type == "increase" else -adjustment_quantity
            
            # 更新商品或变体的库存数量
            async with get_session_for_script() as db:
                if variant_id:
                    # 更新变体库存
                    variant_repo = ProductVariantRepository(db)
                    await variant_repo.update_stock_quantity(variant_id, quantity_change)
                    logger.info(f"已更新变体库存: variant_id={variant_id}, change={quantity_change}")
                else:
                    # 更新商品库存
                    product_repo = ProductRepository(db)
                    await product_repo.update_stock_quantity(product_id, quantity_change)
                    logger.info(f"已更新商品库存: product_id={product_id}, change={quantity_change}")
                
                await db.commit()
            
            # 检查是否需要发送库存警告
            current_quantity = inventory_data.get("available_quantity", 0)
            if current_quantity <= 10:  # 可配置的低库存阈值
                logger.warning(f"库存不足警告: inventory_id={inventory_id}, 当前库存={current_quantity}")
                # 这里可以发送库存不足通知
            
        except Exception as e:
            logger.error(f"处理库存调整事件失败: {str(e)}", exc_info=True)
    
    @staticmethod
    @local_handler.register(event_name="products:inventory:reserved")
    async def handle_inventory_reserved(event: Event):
        """处理库存预留事件
        
        当库存被预留时触发，执行以下操作：
        1. 记录预留日志
        2. 发送预留通知
        3. 检查库存状态
        
        Args:
            event: 事件对象，包含库存预留数据
        """
        try:
            logger.info(f"处理库存预留事件: {event}")
            
            inventory_data = event[1]  # 获取事件数据
            inventory_id = inventory_data.get("id")
            product_id = inventory_data.get("product_id")
            variant_id = inventory_data.get("variant_id")
            reserved_quantity = inventory_data.get("reserved_quantity")
            order_id = inventory_data.get("order_id")
            
            logger.info(f"库存已预留: ID={inventory_id}, 预留数量={reserved_quantity}, 订单ID={order_id}")
            
            # 检查剩余可用库存
            available_quantity = inventory_data.get("available_quantity", 0)
            if available_quantity <= 5:  # 可配置的低库存阈值
                logger.warning(f"预留后库存不足: inventory_id={inventory_id}, 可用库存={available_quantity}")
                # 这里可以发送库存不足通知
            
        except Exception as e:
            logger.error(f"处理库存预留事件失败: {str(e)}", exc_info=True)
    
    @staticmethod
    @local_handler.register(event_name="products:inventory:released")
    async def handle_inventory_released(event: Event):
        """处理库存释放事件
        
        当预留库存被释放时触发，执行以下操作：
        1. 记录释放日志
        2. 发送释放通知
        
        Args:
            event: 事件对象，包含库存释放数据
        """
        try:
            logger.info(f"处理库存释放事件: {event}")
            
            inventory_data = event[1]  # 获取事件数据
            inventory_id = inventory_data.get("id")
            released_quantity = inventory_data.get("released_quantity")
            order_id = inventory_data.get("order_id")
            
            logger.info(f"库存已释放: ID={inventory_id}, 释放数量={released_quantity}, 订单ID={order_id}")
            
        except Exception as e:
            logger.error(f"处理库存释放事件失败: {str(e)}", exc_info=True)
