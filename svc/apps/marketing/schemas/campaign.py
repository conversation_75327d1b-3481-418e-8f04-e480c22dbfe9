from pydantic import BaseModel, Field, ConfigDict
from typing import List, Optional, Dict, Any
from datetime import datetime
from svc.core.schemas.base import PaginatedResponse

class RewardStrategyBase(BaseModel):
    """奖励策略基础模型"""
    model_config = ConfigDict(
        str_strip_whitespace=True,
        json_schema_extra={
            "example": {
                "name": "基础邀请奖励",
                "description": "邀请新用户奖励策略",
                "reward_type": "cash",
                "is_for_inviter": True,
                "is_for_invitee": False,
                "base_reward": 100.0,
                "percentage_rate": None,
                "tiered_config": None,
                "min_invitations": None,
                "max_rewards": None
            }
        }
    )
    
    name: str = Field(..., description="策略名称")
    description: Optional[str] = Field(default=None, description="策略描述")
    reward_type: str = Field(..., description="奖励类型")
    is_for_inviter: bool = Field(default=True, description="是否给邀请人")
    is_for_invitee: bool = Field(default=False, description="是否给被邀请人")
    base_reward: float = Field(..., description="基础奖励值")
    percentage_rate: Optional[float] = Field(default=None, description="百分比率")
    tiered_config: Optional[List[Dict[str, Any]]] = Field(default=None, description="阶梯配置")
    min_invitations: Optional[int] = Field(default=None, description="最小邀请数")
    max_rewards: Optional[int] = Field(default=None, description="最大奖励数")

class CampaignBase(BaseModel):
    """活动基础模型"""
    model_config = ConfigDict(
        str_strip_whitespace=True,
        json_schema_extra={
            "example": {
                "name": "春季邀请活动",
                "description": "2024年春季新用户邀请活动",
                "start_date": "2024-03-01T00:00:00",
                "end_date": "2024-05-31T23:59:59",
                "max_participants": 1000,
                "max_rewards_per_user": 10,
                "anti_abuse_strategy": "none",
                "allow_multi_invitations": True,
                "invitation_limit_per_user": 20
            }
        }
    )
    
    name: str = Field(..., description="活动名称")
    description: Optional[str] = Field(default=None, description="活动描述")
    start_date: Optional[datetime] = Field(default=None, description="开始时间")
    end_date: Optional[datetime] = Field(default=None, description="结束时间")
    max_participants: Optional[int] = Field(default=None, description="最大参与人数")
    max_rewards_per_user: Optional[int] = Field(default=None, description="每用户最大奖励数")
    anti_abuse_strategy: str = Field(default="none", description="防滥用策略")
    allow_multi_invitations: bool = Field(default=True, description="允许多次邀请")
    invitation_limit_per_user: Optional[int] = Field(default=None, description="每用户邀请限制")

class CampaignCreate(CampaignBase):
    """活动创建模型"""
    model_config = ConfigDict(
        str_strip_whitespace=True,
        json_schema_extra={
            "example": {
                "name": "春季邀请活动",
                "description": "2024年春季新用户邀请活动",
                "reward_strategies": []
            }
        }
    )
    
    reward_strategies: Optional[List[RewardStrategyBase]] = Field(default_factory=list, description="奖励策略列表")

class CampaignUpdate(BaseModel):
    """活动更新模型"""
    model_config = ConfigDict(
        str_strip_whitespace=True,
        json_schema_extra={
            "example": {
                "name": "春季邀请活动",
                "status": "active"
            }
        }
    )
    
    name: Optional[str] = Field(default=None, description="活动名称")
    description: Optional[str] = Field(default=None, description="活动描述")
    start_date: Optional[datetime] = Field(default=None, description="开始时间")
    end_date: Optional[datetime] = Field(default=None, description="结束时间")
    status: Optional[str] = Field(default=None, description="活动状态")
    max_participants: Optional[int] = Field(default=None, description="最大参与人数")
    max_rewards_per_user: Optional[int] = Field(default=None, description="每用户最大奖励数")
    anti_abuse_strategy: Optional[str] = Field(default=None, description="防滥用策略")
    allow_multi_invitations: Optional[bool] = Field(default=None, description="允许多次邀请")
    invitation_limit_per_user: Optional[int] = Field(default=None, description="每用户邀请限制")

class RewardStrategyResponse(RewardStrategyBase):
    """奖励策略响应模型"""
    model_config = ConfigDict(
        from_attributes=True,
        str_strip_whitespace=True,
        json_schema_extra={
            "example": {
                "id": 1,
                "campaign_id": 1,
                "created_at": "2024-03-24T12:00:00",
                "updated_at": "2024-03-24T12:00:00"
            }
        }
    )
    
    id: int = Field(description="策略ID")
    campaign_id: int = Field(description="活动ID")
    created_at: datetime = Field(description="创建时间")
    updated_at: datetime = Field(description="更新时间")

class CampaignResponse(CampaignBase):
    """活动响应模型"""
    model_config = ConfigDict(
        from_attributes=True,
        str_strip_whitespace=True,
        json_schema_extra={
            "example": {
                "id": 1,
                "creator_id": 1,
                "status": "active",
                "participant_count": 100,
                "invitation_count": 150,
                "reward_count": 80,
                "total_reward_value": 8000.0,
                "created_at": "2024-03-24T12:00:00",
                "updated_at": "2024-03-24T12:00:00"
            }
        }
    )
    
    id: int = Field(description="活动ID")
    creator_id: int = Field(description="创建者ID")
    status: str = Field(description="活动状态")
    participant_count: int = Field(description="参与人数")
    invitation_count: int = Field(description="邀请数量")
    reward_count: int = Field(description="奖励数量")
    total_reward_value: float = Field(description="总奖励值")
    created_at: datetime = Field(description="创建时间")
    updated_at: datetime = Field(description="更新时间")

class CampaignConfigResponse(BaseModel):
    """活动配置响应模型"""
    model_config = ConfigDict(
        str_strip_whitespace=True,
        json_schema_extra={
            "example": {
                "id": 1,
                "name": "春季邀请活动",
                "description": "2024年春季新用户邀请活动",
                "start_date": "2024-03-01T00:00:00",
                "end_date": "2024-05-31T23:59:59",
                "status": "active",
                "anti_abuse_strategy": "none",
                "allow_multi_invitations": True,
                "invitation_limit_per_user": 20,
                "max_participants": 1000,
                "max_rewards_per_user": 10,
                "current_participants": 100,
                "reward_strategies": []
            }
        }
    )
    
    id: int = Field(description="活动ID")
    name: str = Field(description="活动名称")
    description: Optional[str] = Field(default=None, description="活动描述")
    start_date: Optional[str] = Field(default=None, description="开始时间")
    end_date: Optional[str] = Field(default=None, description="结束时间")
    status: str = Field(description="活动状态")
    anti_abuse_strategy: str = Field(description="防滥用策略")
    allow_multi_invitations: bool = Field(description="允许多次邀请")
    invitation_limit_per_user: Optional[int] = Field(default=None, description="每用户邀请限制")
    max_participants: Optional[int] = Field(default=None, description="最大参与人数")
    max_rewards_per_user: Optional[int] = Field(default=None, description="每用户最大奖励数")
    current_participants: int = Field(description="当前参与人数")
    reward_strategies: List[Dict[str, Any]] = Field(description="奖励策略列表")

class CampaignListResponse(PaginatedResponse[CampaignResponse]):
    """活动列表响应模型"""
    pass 

class GetCampaignsParams(BaseModel):
    """获取活动列表的查询参数模型"""
    model_config = ConfigDict(
        str_strip_whitespace=True,
        extra='ignore'  # 忽略未定义的查询参数
    )
    
    page_num: int = Field(1, ge=1, description="页码")
    page_size: int = Field(10, ge=1, le=1000, description="每页数量")
    status: Optional[str] = Field(default=None, description="活动状态")
    # 可以根据需要添加更多过滤和排序字段
    # order_by: Optional[str] = Field(default="created_at", description="排序字段")
    # order_desc: Optional[bool] = Field(default=True, description="是否降序") 