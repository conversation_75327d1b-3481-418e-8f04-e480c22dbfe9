"""
商品变体模型，定义商品变体的基本属性和规则
"""
from sqlalchemy import Column, BigInteger, String, DateTime, Boolean, Integer, Float, ForeignKey
from sqlalchemy.orm import relationship
from typing import Dict

from svc.core.database.session import Base
from svc.core.models.custom_types import DatabaseCompatibleJSON
from svc.core.utils.datetime_utils import get_utc_now_without_tzinfo

class ProductVariant(Base):
    """
    商品变体模型，定义商品变体的基本属性和规则
    用于支持商品的不同规格、颜色、尺寸等变体
    """
    __tablename__ = "product_variants"
    
    id = Column(BigInteger, primary_key=True, autoincrement=True)
    product_id = Column(BigInteger, ForeignKey("products.id"), nullable=False, comment="商品ID")
    
    # 变体基本信息
    name = Column(String(200), nullable=False, comment="变体名称")
    sku = Column(String(100), nullable=False, unique=True, index=True, comment="变体SKU")
    barcode = Column(String(100), nullable=True, index=True, comment="变体条码")
    
    # 价格信息
    price = Column(Float, nullable=True, comment="变体价格，为空则使用商品价格")
    cost_price = Column(Float, nullable=True, comment="变体成本价格")
    market_price = Column(Float, nullable=True, comment="变体市场价格")
    
    # 库存信息
    stock_quantity = Column(Integer, default=0, comment="变体库存数量")
    min_stock_level = Column(Integer, default=0, comment="变体最低库存警戒线")
    
    # 变体属性
    weight = Column(Float, nullable=True, comment="变体重量(kg)")
    dimensions = Column(DatabaseCompatibleJSON, default=dict, comment="变体尺寸信息")
    attributes = Column(DatabaseCompatibleJSON, default=dict, comment="变体属性(颜色、尺寸等)")
    
    # 状态
    is_active = Column(Boolean, default=True, comment="是否启用")
    is_default = Column(Boolean, default=False, comment="是否为默认变体")
    
    # 排序
    sort_order = Column(Integer, default=0, comment="排序顺序")
    
    # 创建和更新时间
    created_at = Column(DateTime, nullable=False, default=get_utc_now_without_tzinfo)
    updated_at = Column(DateTime, nullable=False, default=get_utc_now_without_tzinfo, 
                        onupdate=get_utc_now_without_tzinfo)
    
    # 关系
    product = relationship("Product", back_populates="variants", lazy="selectin")
    inventories = relationship("Inventory", back_populates="variant", cascade="all, delete-orphan", lazy="dynamic")
    
    def is_available(self) -> bool:
        """判断变体是否可用"""
        if not self.is_active:
            return False
            
        if self.stock_quantity <= 0:
            return False
            
        return True
        
    def is_low_stock(self) -> bool:
        """判断是否库存不足"""
        return self.stock_quantity <= self.min_stock_level
        
    def get_effective_price(self) -> float:
        """获取有效价格，优先使用变体价格，否则使用商品价格"""
        return self.price if self.price is not None else (self.product.price if self.product else 0.0)
        
    def to_dict(self) -> Dict:
        """将变体对象转换为字典，用于JSON序列化和缓存
        
        Returns:
            dict: 变体的字典表示
        """
        return {
            "id": self.id,
            "product_id": self.product_id,
            "name": self.name,
            "sku": self.sku,
            "barcode": self.barcode,
            "price": self.price,
            "cost_price": self.cost_price,
            "market_price": self.market_price,
            "stock_quantity": self.stock_quantity,
            "min_stock_level": self.min_stock_level,
            "weight": self.weight,
            "dimensions": self.dimensions,
            "attributes": self.attributes,
            "is_active": self.is_active,
            "is_default": self.is_default,
            "sort_order": self.sort_order,
            "created_at": self.created_at.isoformat() if self.created_at else None,
            "updated_at": self.updated_at.isoformat() if self.updated_at else None
        }
