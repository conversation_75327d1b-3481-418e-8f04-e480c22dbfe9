"""
健康检查路由模块。
提供系统健康状态检查的API接口。
"""

from typing import Any, Dict, List

from fastapi import APIRouter, Depends, Query
from pydantic import BaseModel, Field
from sqlalchemy.ext.asyncio import AsyncSession

from svc.apps.system.dependencies import get_system_service
from svc.apps.system.services.system import SystemService
from svc.core.services.result import Result

# 创建路由器
router = APIRouter(tags=["系统监控"])

# 定义响应模型
class HealthCheckItem(BaseModel):
    """健康检查项目模型"""
    name: str = Field(..., description="检查项名称")
    status: str = Field(..., description="状态：healthy, unhealthy, warning, unknown")
    details: Dict[str, Any] = Field(default={}, description="详细信息")

class HealthCheckResponse(BaseModel):
    """健康检查响应模型"""
    status: str = Field(..., description="整体状态：healthy, unhealthy, warning, unknown")
    timestamp: str = Field(..., description="检查时间")
    services: Dict[str, str] = Field(..., description="服务状态")
    details: List[HealthCheckItem] = Field(default=[], description="详细检查项")

# === 路由处理函数 ===

@router.get("", response_model=Result[Dict[str, Any]], summary="获取健康状态")
async def get_health_status(
    system_service: SystemService = Depends(get_system_service)
):
    """
    获取系统健康状态
    
    返回系统的健康状态信息，包括各个服务组件的状态。
    
    - **status**: 整体健康状态 (healthy, unhealthy, warning, unknown)
    - **services**: 各服务组件状态
    - **timestamp**: 检查时间
    """
    return await system_service.get_health_status()

@router.get("/detailed", response_model=Result[Dict[str, Any]], summary="获取详细健康状态")
async def get_detailed_health_status(
    check_db: bool = Query(True, description="是否检查数据库"),
    check_redis: bool = Query(True, description="是否检查Redis"),
    check_storage: bool = Query(False, description="是否检查存储"),
    system_service: SystemService = Depends(get_system_service)
):
    """
    获取详细的系统健康状态
    
    返回系统的详细健康状态信息，包括各个服务组件的状态和详细指标。
    可以通过查询参数控制需要检查的组件。
    
    - **status**: 整体健康状态 (healthy, unhealthy, warning, unknown)
    - **services**: 各服务组件状态
    - **details**: 详细检查项信息
    - **timestamp**: 检查时间
    """
    # 此处可以扩展SystemService以提供更详细的健康检查
    # 目前调用基本健康检查
    return await system_service.get_health_status()

@router.get("/liveness", status_code=204, summary="存活检查")
async def liveness_check():
    """
    简单的存活检查
    
    用于Kubernetes等容器编排平台的liveness probe。
    如果服务正在运行，返回204 No Content。
    """
    # 简单返回，不需要任何检查
    return None

@router.get("/readiness", response_model=Result[Dict[str, Any]], summary="就绪检查")
async def readiness_check(
    system_service: SystemService = Depends(get_system_service)
):
    """
    就绪状态检查
    
    用于Kubernetes等容器编排平台的readiness probe。
    检查系统是否已准备好处理请求，包括数据库连接等依赖服务是否正常。
    """
    return await system_service.get_health_status()


@router.get("/info", response_model=Result[Dict[str, Any]], summary="获取系统信息")
async def get_system_info(
    system_service: SystemService = Depends(get_system_service)
):
    """
    获取系统信息

    返回系统基本信息，包括：
    - **application**: 应用信息（名称、版本、环境、运行时间）
    - **system**: 系统信息（平台、Python版本、架构等）
    - **resources**: 资源使用情况
    - **timestamp**: 获取时间
    """
    return await system_service.get_system_info()


@router.get("/metrics", response_model=Result[Dict[str, Any]], summary="获取性能指标")
async def get_performance_metrics(
    system_service: SystemService = Depends(get_system_service)
):
    """
    获取系统性能指标

    返回详细的性能指标，包括：
    - **cpu**: CPU使用率、核心数、负载
    - **memory**: 内存使用情况
    - **disk**: 磁盘使用情况
    - **network**: 网络统计
    - **database**: 数据库性能指标
    - **redis**: Redis性能指标
    - **timestamp**: 获取时间
    """
    return await system_service.get_performance_metrics()