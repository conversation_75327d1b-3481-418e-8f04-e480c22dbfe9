"""
用户管理API路由
"""
from typing import Any, List, Dict, Optional

from fastapi import APIRouter, Depends, Query, Path,status


from svc.core.services.result import Result
from svc.core.database import transactional_route
from svc.apps.auth.schemas import (
    UserCreate, 
    UserUpdate, 
    UserResponse, 
    UserWithRoles,
    RoleBase,
    GetUsersParams,
    UserListResponse,
    CreateUserParams,
    UpdateUserParams,
    DeleteUserParams,
    AssignRoleParams,
    RemoveRoleParams,

)
from svc.apps.auth.services import (
    UserService,
    RoleService
)
from svc.apps.auth.dependencies import (
    get_user_service,
    get_current_active_user,
    get_current_superuser,
   
)
from svc.apps.auth.models.user import User
from svc.core.exceptions import (
    handle_route_errors,
    USER_ERROR_MAPPING
)

router = APIRouter(tags=["用户管理"])

# === 客户端路由 (Current User - /users/me/*) ===

@router.get("/me", response_model=Result[UserWithRoles]) # Path: /users/me - Return detailed info for current user
@handle_route_errors(USER_ERROR_MAPPING)
async def get_my_profile(
    user_service: UserService = Depends(get_user_service),
    current_user: User = Depends(get_current_active_user),
) -> Result[UserWithRoles]:
    """获取当前用户的详细资料（包含角色）"""
    user_result = await user_service.get_user(current_user.id)
    return user_result

@router.put("/me", response_model=Result[UserResponse]) # Path: /users/me (Update current user)
@transactional_route()
@handle_route_errors(USER_ERROR_MAPPING)
async def update_my_profile(
    user_in: UserUpdate, # Schema for user self-update (might differ from admin update)
    user_service: UserService = Depends(get_user_service),
    current_user: User = Depends(get_current_active_user),
) -> Result[UserResponse]:
    """更新当前用户的个人资料"""
    # Ensure user cannot update certain fields like is_active, is_superuser, roles via this endpoint
    update_params = UpdateUserParams(
        user_id=current_user.id,
        user_data=user_in
    )
    # Service layer should enforce restrictions on updatable fields for non-admins
    return await user_service.update_user(update_params, allowed_by_admin=False)


# === 管理端路由 (Admin Routes - /users/admin/*) ===

@router.get("/admin/", response_model=Result[UserListResponse]) # Path: /users/admin/
@handle_route_errors(USER_ERROR_MAPPING)
async def admin_list_users(
    user_service: UserService = Depends(get_user_service),
    current_user: User = Depends(get_current_superuser), # Or check permission
    page_num: int = Query(1, ge=1, description="页码"),
    page_size: int = Query(10, ge=1, le=1000, description="每页数量"),
) -> Result[UserListResponse]:
    """获取用户列表 (管理端)"""
    params = GetUsersParams(
        page_num=page_num,
        page_size=page_size,
        order_by="created_at",
        order_desc=True
    )
    return await user_service.get_users(params)

@router.get("/admin/details/{user_id}", response_model=Result[UserWithRoles]) # Path: /users/admin/details/{user_id}
@handle_route_errors(USER_ERROR_MAPPING)
async def admin_get_user_details(
    user_id: int = Path(..., description="用户ID"),
    user_service: UserService = Depends(get_user_service),
    current_user: User = Depends(get_current_superuser), # Or check permission
    ) -> Result[UserWithRoles]:
    """获取任意用户的详细资料（包含角色） (管理端)"""
    user_result = await user_service.get_user(user_id)
    return user_result

@router.post("/admin/", response_model=Result[UserResponse]) # Path: /users/admin/
@transactional_route(auto_commit=True)
@handle_route_errors(USER_ERROR_MAPPING)
async def admin_create_user(
    user_in: UserCreate, # Admin might have a different UserCreate schema (e.g., setting roles)
    user_service: UserService = Depends(get_user_service),
    current_user: User = Depends(get_current_superuser), # Or check permission
) -> Result[UserResponse]:
    """创建新用户 (管理端)"""
    create_params = CreateUserParams(
        user_data=user_in,
        send_welcome_email=False # Or make configurable
    )
    return await user_service.create_user(create_params)

@router.put("/admin/{user_id}", response_model=Result[UserResponse]) # Path: /users/admin/{user_id}
@transactional_route()
@handle_route_errors(USER_ERROR_MAPPING)
async def admin_update_user(
    user_in: UserUpdate, # Admin might have a different UserUpdate schema
    user_id: int = Path(..., description="用户ID"),
    user_service: UserService = Depends(get_user_service),
    current_user: User = Depends(get_current_superuser), # Or check permission
) -> Result[UserResponse]:
    """更新任意用户信息 (管理端)"""
    update_params = UpdateUserParams(
        user_id=user_id,
        user_data=user_in
    )
    # Allow admin to update sensitive fields
    return await user_service.update_user(update_params, allowed_by_admin=True)

@router.delete("/admin/{user_id}", response_model=Result) # Path: /users/admin/{user_id}
@transactional_route()
@handle_route_errors(USER_ERROR_MAPPING)
async def admin_delete_user(
    user_id: int = Path(..., description="用户ID"),
    permanent: bool = Query(False, description="是否永久删除"),
    user_service: UserService = Depends(get_user_service),
    current_user: User = Depends(get_current_superuser), # Or check permission
) -> Result:
    """删除用户 (管理端)"""
    delete_params = DeleteUserParams(
        user_id=user_id,
        executor_id=current_user.id,
        permanent=permanent
    )
    return await user_service.delete_user(delete_params)

@router.post("/admin/assign-role/{user_id}/{role_id}", response_model=Result) # Path: /users/admin/assign-role/{user_id}/{role_id}
@transactional_route(auto_commit=True)
@handle_route_errors(USER_ERROR_MAPPING)
async def admin_assign_role_to_user(
    user_id: int = Path(..., description="用户ID"),
    role_id: int = Path(..., description="角色ID"),
    user_service: UserService = Depends(get_user_service),
    current_user: User = Depends(get_current_superuser), # Or check permission
) -> Result:
    """为用户分配角色 (管理端)"""
    assign_params = AssignRoleParams(
        user_id=user_id,
        role_id=role_id,
        executor_id=current_user.id
    )
    return await user_service.assign_role(assign_params)

@router.delete("/admin/remove-role/{user_id}/{role_id}", response_model=Result) # Path: /users/admin/remove-role/{user_id}/{role_id}
@transactional_route(auto_commit=True)
@handle_route_errors(USER_ERROR_MAPPING)
async def admin_remove_role_from_user(
    user_id: int = Path(..., description="用户ID"),
    role_id: int = Path(..., description="角色ID"),
    user_service: UserService = Depends(get_user_service),
    current_user: User = Depends(get_current_superuser), # Or check permission
) -> Result:
    """从用户移除角色 (管理端)"""
    remove_params = RemoveRoleParams(
        user_id=user_id,
        role_id=role_id,
        executor_id=current_user.id
    )
    return await user_service.remove_role(remove_params)
