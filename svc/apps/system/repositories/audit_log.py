"""
审计日志仓库实现。
提供对审计日志数据的访问和操作。
"""

from datetime import datetime
from typing import Any, Dict, List, Optional, Tuple

from sqlalchemy import and_, desc, func, select
from sqlalchemy.ext.asyncio import AsyncSession

from svc.apps.system.models.audit_log import AuditLog
from svc.core.repositories import BaseRepository


class AuditLogRepository(BaseRepository[AuditLog, Dict[str, Any], Dict[str, Any]]):
    """
    审计日志仓库类
    
    提供对审计日志数据的访问和操作，支持按条件查询和过滤。
    """
    
    def __init__(self, db: AsyncSession):
        """初始化审计日志仓库"""
        super().__init__(db, AuditLog)
    
    async def create(self, db: AsyncSession, data: Dict[str, Any]) -> AuditLog:
        """
        创建审计日志
        
        Args:
            db: 数据库会话
            data: 审计日志数据
            
        Returns:
            AuditLog: 创建的审计日志实例
        """
        log = AuditLog(**data)
        db.add(log)
        await db.commit()
        await db.refresh(log)
        return log
    
    async def get_by_id(self, db: AsyncSession, log_id: int) -> Optional[AuditLog]:
        """
        按ID获取审计日志
        
        Args:
            db: 数据库会话
            log_id: 审计日志ID
            
        Returns:
            Optional[AuditLog]: 审计日志实例，不存在则返回None
        """
        query = select(AuditLog).where(AuditLog.id == log_id)
        result = await db.execute(query)
        return result.scalars().first()
    
    async def search(
        self,
        db: AsyncSession,
        action: Optional[str] = None,
        resource_type: Optional[str] = None,
        resource_id: Optional[str] = None,
        user_id: Optional[int] = None,
        username: Optional[str] = None,
        status: Optional[str] = None,
        start_date: Optional[datetime] = None,
        end_date: Optional[datetime] = None,
        page: int = 1,
        page_size: int = 20
    ) -> Tuple[List[AuditLog], int]:
        """
        按条件搜索审计日志
        
        Args:
            db: 数据库会话
            action: 操作类型
            resource_type: 资源类型
            resource_id: 资源ID
            user_id: 用户ID
            username: 用户名
            status: 操作结果
            start_date: 开始日期
            end_date: 结束日期
            page: 页码，从1开始
            page_size: 每页数量
            
        Returns:
            Tuple[List[AuditLog], int]: 审计日志列表和总数
        """
        # 构建条件
        conditions = []
        if action:
            conditions.append(AuditLog.action == action)
        if resource_type:
            conditions.append(AuditLog.resource_type == resource_type)
        if resource_id:
            conditions.append(AuditLog.resource_id == resource_id)
        if user_id:
            conditions.append(AuditLog.user_id == user_id)
        if username:
            conditions.append(AuditLog.username.ilike(f"%{username}%"))
        if status:
            conditions.append(AuditLog.status == status)
        if start_date:
            conditions.append(AuditLog.created_at >= start_date)
        if end_date:
            conditions.append(AuditLog.created_at <= end_date)
        
        # 查询总数
        count_query = select(func.count()).select_from(AuditLog)
        if conditions:
            count_query = count_query.where(and_(*conditions))
        
        total_result = await db.execute(count_query)
        total = total_result.scalar() or 0
        
        # 查询数据
        query = select(AuditLog)
        if conditions:
            query = query.where(and_(*conditions))
        
        # 按创建时间倒序排序
        query = query.order_by(desc(AuditLog.created_at))
        
        # 分页
        query = query.offset((page - 1) * page_size).limit(page_size)
        
        result = await db.execute(query)
        logs = result.scalars().all()
        
        return list(logs), total
    
    async def get_latest_logs(
        self, 
        db: AsyncSession, 
        limit: int = 10
    ) -> List[AuditLog]:
        """
        获取最新的审计日志
        
        Args:
            db: 数据库会话
            limit: 返回数量限制
            
        Returns:
            List[AuditLog]: 审计日志列表
        """
        query = select(AuditLog).order_by(desc(AuditLog.created_at)).limit(limit)
        result = await db.execute(query)
        return list(result.scalars().all())
    
    async def get_actions_by_resource(
        self, 
        db: AsyncSession, 
        resource_type: str, 
        resource_id: str
    ) -> List[AuditLog]:
        """
        获取某个资源的所有操作记录
        
        Args:
            db: 数据库会话
            resource_type: 资源类型
            resource_id: 资源ID
            
        Returns:
            List[AuditLog]: 审计日志列表
        """
        query = select(AuditLog).where(
            and_(
                AuditLog.resource_type == resource_type,
                AuditLog.resource_id == resource_id
            )
        ).order_by(desc(AuditLog.created_at))
        
        result = await db.execute(query)
        return list(result.scalars().all())

    async def get_actions_by_user(
        self,
        db: AsyncSession,
        user_id: int,
        limit: int = 50
    ) -> List[AuditLog]:
        """
        获取某个用户的操作记录

        Args:
            db: 数据库会话
            user_id: 用户ID
            limit: 返回数量限制

        Returns:
            List[AuditLog]: 审计日志列表
        """
        query = select(AuditLog).where(
            AuditLog.user_id == user_id
        ).order_by(desc(AuditLog.created_at)).limit(limit)

        result = await db.execute(query)
        return list(result.scalars().all())

    async def get_statistics(
        self,
        db: AsyncSession,
        start_date: datetime,
        end_date: datetime
    ) -> Dict[str, Any]:
        """
        获取审计日志统计信息

        Args:
            db: 数据库会话
            start_date: 开始日期
            end_date: 结束日期

        Returns:
            Dict[str, Any]: 统计信息
        """
        # 总数统计
        total_query = select(func.count()).select_from(AuditLog).where(
            and_(
                AuditLog.created_at >= start_date,
                AuditLog.created_at <= end_date
            )
        )
        total_result = await db.execute(total_query)
        total_count = total_result.scalar() or 0

        # 按操作类型统计
        action_query = select(
            AuditLog.action,
            func.count().label('count')
        ).where(
            and_(
                AuditLog.created_at >= start_date,
                AuditLog.created_at <= end_date
            )
        ).group_by(AuditLog.action)

        action_result = await db.execute(action_query)
        action_stats = {row.action: row.count for row in action_result}

        # 按资源类型统计
        resource_query = select(
            AuditLog.resource_type,
            func.count().label('count')
        ).where(
            and_(
                AuditLog.created_at >= start_date,
                AuditLog.created_at <= end_date
            )
        ).group_by(AuditLog.resource_type)

        resource_result = await db.execute(resource_query)
        resource_stats = {row.resource_type: row.count for row in resource_result}

        # 按状态统计
        status_query = select(
            AuditLog.status,
            func.count().label('count')
        ).where(
            and_(
                AuditLog.created_at >= start_date,
                AuditLog.created_at <= end_date
            )
        ).group_by(AuditLog.status)

        status_result = await db.execute(status_query)
        status_stats = {row.status: row.count for row in status_result}

        return {
            "total_count": total_count,
            "date_range": {
                "start_date": start_date.isoformat(),
                "end_date": end_date.isoformat()
            },
            "by_action": action_stats,
            "by_resource_type": resource_stats,
            "by_status": status_stats
        }

    async def delete_old_logs(
        self,
        db: AsyncSession,
        cutoff_date: datetime
    ) -> int:
        """
        删除旧的审计日志

        Args:
            db: 数据库会话
            cutoff_date: 截止日期，早于此日期的日志将被删除

        Returns:
            int: 删除的记录数
        """
        from sqlalchemy import delete

        delete_query = delete(AuditLog).where(
            AuditLog.created_at < cutoff_date
        )

        result = await db.execute(delete_query)
        await db.commit()

        return result.rowcount or 0