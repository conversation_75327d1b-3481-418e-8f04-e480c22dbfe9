"""
系统模块
包含系统配置、监控、审计日志和健康检查功能
"""

# 导出ORM模型
from svc.apps.system.models.config_item import ConfigItem
from svc.apps.system.models.audit_log import AuditLog

# 导出Schema模型
from svc.apps.system.schemas.config import (
    # 配置项模型
    ConfigItemBase,
    ConfigItemCreate,
    ConfigItemUpdate,
    ConfigItemResponse
)

from svc.apps.system.schemas.audit_log import (
    # 审计日志模型
    AuditLogBase,
    AuditLogCreate,
    AuditLogInDB,
    AuditLogResponse,
    AuditLogListResponse,
    AuditLogFilter
)

# 导出服务类
from svc.apps.system.services.system import SystemService
from svc.apps.system.services.config import ConfigService
from svc.apps.system.services.audit_log import AuditLogService

# 导出仓库类
from svc.apps.system.repositories.config import ConfigRepository
from svc.apps.system.repositories.audit_log import AuditLogRepository

# 导出依赖函数
from svc.apps.system.dependencies import (
    get_system_service,
    get_config_service,
    get_config_repository,
    get_audit_log_service,
    get_audit_log_repository,
)

# 错误处理
from svc.core.exceptions import (
    ErrorCode,
    handle_route_errors
)

# 导出
__all__ = [
    # 模型
    "ConfigItem", "AuditLog",
    
    # Schema
    "ConfigItemBase", "ConfigItemCreate", "ConfigItemUpdate", "ConfigItemResponse",
    "AuditLogBase", "AuditLogCreate", "AuditLogInDB", "AuditLogResponse",
    "AuditLogListResponse", "AuditLogFilter",
    
    # 服务类
    "SystemService", "ConfigService", "AuditLogService",
    
    # 仓库类
    "ConfigRepository", "AuditLogRepository",
    
    # 依赖函数
    "get_system_service", "get_config_service", "get_config_repository",
    "get_audit_log_service", "get_audit_log_repository",
    
    # 错误处理
    "ErrorCode", "handle_route_errors"
]
