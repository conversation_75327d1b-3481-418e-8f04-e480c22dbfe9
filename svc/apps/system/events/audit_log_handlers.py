"""
系统审计日志事件处理器
"""

import logging

from fastapi_events.handlers.local import local_handler
from fastapi_events.typing import Event

from svc.core.events.event_names import \
    SYSTEM_AUDIT_LOG_RECORDED  # SYSTEM_AUDIT_LOG_REQUESTED # 服务层直接调用服务记录，事件处理器仅监听已记录事件; 监听此事件以确认记录

logger = logging.getLogger(__name__)

# 不再需要处理 SYSTEM_AUDIT_LOG_REQUESTED，服务层应直接调用 AuditLogService.create_audit_log
# @local_handler.register(event_name=SYSTEM_AUDIT_LOG_REQUESTED)
# async def handle_audit_log_request(
#     event: Event,
#     # audit_service: AuditLogService = Depends(get_audit_log_service)
# ):
#     event_name, payload = event
#     user_id = payload.get('user_id')
#     action = payload.get('action')
#     resource_type = payload.get('resource_type')
#     resource_id = payload.get('resource_id')
#     metadata = payload.get('metadata', {})
#
#     if not user_id or not action:
#         logger.error(f"处理审计日志记录请求失败: 缺少 user_id 或 action. Payload: {payload}")
#         return
#
#     try:
#         # 调用服务记录日志
#         # await audit_service.record_log(
#         #     user_id=user_id,
#         #     action=action,
#         #     resource_type=resource_type,
#         #     resource_id=resource_id,
#         #     metadata=metadata
#         # )
#         logger.info(f"审计日志记录请求已处理: action={action}, user_id={user_id}")
#         # 可以在这里触发 SYSTEM_AUDIT_LOG_RECORDED 事件，如果服务层不触发的话
#         # dispatch(SYSTEM_AUDIT_LOG_RECORDED, payload=payload)
#     except Exception as e:
#         logger.error(f"处理审计日志记录请求时出错: {e}", exc_info=True)


# 处理已记录审计日志的事件 (用于后续处理，如聚合、通知等)
@local_handler.register(event_name=SYSTEM_AUDIT_LOG_RECORDED)
async def handle_audit_log_recorded(
    event: Event,
    # audit_service: AuditLogService = Depends(get_audit_log_service) # 注入 AuditLogService
    # 可以在这里注入其他需要的服务
):
    """处理审计日志已被记录的事件。"""
    _, payload = event
    log_id = payload.get('log_id') # 假设服务层记录后会返回日志 ID
    user_id = payload.get('user_id')
    action = payload.get('action')
    resource_type = payload.get('resource_type')
    resource_id = payload.get('resource_id')
    status = payload.get('status')

    # 记录接收到的事件信息
    logger.info(
        f"[Event Handler] 审计日志已记录: log_id={log_id}, action={action}, "
        f"user_id={user_id}, resource={resource_type}:{resource_id}, status={status}"
    )

    # 在这里可以添加额外的逻辑，例如：
    # 1. 触发基于特定审计事件的通知 (例如，关键操作失败)
    # if status == 'failed' and action in ['user_delete', 'config_update']:
    #     try:
    #         dispatch(
    #             "system:security:alert_triggered",
    #             payload={
    #                 "alert_type": "critical_action_failed",
    #                 "log_id": log_id,
    #                 "action": action,
    #                 "user_id": user_id,
    #                 "resource_type": resource_type,
    #                 "resource_id": resource_id,
    #                 "message": payload.get('message', 'Unknown error')
    #             }
    #         )
    #     except Exception as e:
    #         logger.error(f"触发安全警报失败 (log_id={log_id}): {e}", exc_info=True)

    # 2. 更新相关实体的状态或统计 (如果需要，但通常统计更新由 SYSTEM_STATS_UPDATE_REQUESTED 处理)

    # 3. 将日志推送到外部监控系统 (例如 ELK, Splunk)
    # try:
    #     # external_monitoring_client.push_log(payload)
    #     pass
    # except Exception as e:
    #     logger.error(f"推送审计日志到外部系统失败 (log_id={log_id}): {e}", exc_info=True)

    # 注意：确保这里的逻辑不会再次触发 SYSTEM_AUDIT_LOG_RECORDED，避免无限循环

    pass # Placeholder for actual logic