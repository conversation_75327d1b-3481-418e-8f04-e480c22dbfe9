"""
商品管理模块数据模型

该模块包含商品管理相关的数据模型定义：
- Product: 商品模型
- Category: 商品分类模型
- ProductVariant: 商品变体模型
- Inventory: 库存模型
"""

from .product import Product, ProductStatus
from .category import Category, CategoryStatus
from .product_variant import ProductVariant
from .inventory import Inventory, InventoryStatus

__all__ = [
    "Product",
    "ProductStatus", 
    "Category",
    "CategoryStatus",
    "ProductVariant",
    "Inventory",
    "InventoryStatus"
]
