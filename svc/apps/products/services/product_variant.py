from typing import Optional

from redis.asyncio import Redis

from svc.apps.products.repositories.product_variant import ProductVariantRepository
from svc.core.exceptions.error_codes import ErrorCode
from svc.core.services import BaseService
from svc.core.services.result import Result
from svc.apps.products.models.product_variant import ProductVariant
from svc.apps.products.schemas.product_variant import (
    ProductVariantResponse,
    ProductVariantCreate,
    ProductVariantUpdate,
    ProductVariantListResponse
)
from fastapi_events.dispatcher import dispatch

# 缓存配置
CACHE_TTL = 3600  # 默认缓存过期时间（1小时）

class ProductVariantService(BaseService[ProductVariant, Result[ProductVariantResponse]]):
    """商品变体服务类，提供变体的创建、查询和管理功能"""
    
    # 设置资源类型名称
    resource_type = "product_variant"
    
    def __init__(
        self, 
        redis: Optional[Redis] = None, 
        variant_repo: Optional[ProductVariantRepository] = None
    ):
        """初始化商品变体服务"""
        super().__init__(redis)
        self.variant_repo = variant_repo
    
    async def get_resource_by_id(self, variant_id: int) -> Optional[ProductVariant]:
        """获取指定ID的变体资源"""
        return await self.variant_repo.get_by_id(variant_id)
    
    async def get_variant(self, variant_id: int) -> Result[ProductVariantResponse]:
        """获取变体信息"""
        try:
            self.logger.info(f"获取变体: id={variant_id}")
            
            # 查询数据库
            variant = await self.get_resource_by_id(variant_id)
            if not variant:
                self.logger.warning(f"变体不存在: id={variant_id}")
                return self.resource_not_found_result(variant_id)
            
            # 构建变体响应
            variant_response = ProductVariantResponse.model_validate(variant.to_dict())
            
            return self.create_success_result(variant_response)
        except Exception as e:
            self.logger.error(f"获取变体失败: id={variant_id}, 错误={str(e)}", exc_info=True)
            return self.create_error_result(
                error_code=ErrorCode.NOT_FOUND,
                error_message=f"获取变体失败: {str(e)}"
            )
    
    async def get_variants_by_product_id(
        self, 
        product_id: int,
        page_num: int = 1,
        page_size: int = 10
    ) -> Result[ProductVariantListResponse]:
        """获取指定商品的变体列表"""
        try:
            skip = (page_num - 1) * page_size
            self.logger.info(f"获取商品变体列表: product_id={product_id}")
            
            # 使用仓库类获取变体列表和总数
            variants, total = await self.variant_repo.get_by_product_id(
                product_id=product_id,
                skip=skip,
                limit=page_size
            )
            
            # 构建变体响应列表
            variant_responses = []
            for variant in variants:
                variant_response = ProductVariantResponse.model_validate(variant.to_dict())
                variant_responses.append(variant_response)
                
            # 计算总页数
            total_pages = (total + page_size - 1) // page_size if page_size > 0 else 0
            
            # 构建分页响应
            paginated_response = ProductVariantListResponse(
                items=variant_responses,
                total=total,
                page=page_num,
                size=page_size,
                pages=total_pages
            )
            
            return self.create_success_result(paginated_response)
        except Exception as e:
            self.logger.error(f"获取变体列表失败: 错误={str(e)}", exc_info=True)
            return self.create_error_result(
                error_code=ErrorCode.OPERATION_FAILED,
                error_message=f"获取变体列表失败: {str(e)}"
            )
    
    async def create_variant(self, params: ProductVariantCreate) -> Result[ProductVariantResponse]:
        """创建变体"""
        try:
            self.logger.info(f"创建变体: name={params.name}, sku={params.sku}")
            
            # 检查SKU是否已存在
            existing_variant = await self.variant_repo.get_by_sku(params.sku)
            if existing_variant:
                self.logger.warning(f"变体SKU已存在: {params.sku}")
                return self.create_error_result(
                    error_code=ErrorCode.ALREADY_EXISTS,
                    error_message=f"变体SKU {params.sku} 已被使用"
                )
            
            # 创建变体
            variant = await self.variant_repo.create(**params.model_dump())
            
            # 构建变体响应
            variant_response = ProductVariantResponse.model_validate(variant.to_dict())
            
            # 触发变体创建事件
            event_data = variant_response.model_dump()
            dispatch("products:variant:created", payload=event_data)
            
            self.logger.info(f"变体创建成功: id={variant.id}, name={variant.name}")
            return self.create_success_result(variant_response)
        except Exception as e:
            self.logger.error(f"创建变体失败: 错误={str(e)}", exc_info=True)
            return self.create_error_result(
                error_code=ErrorCode.OPERATION_FAILED,
                error_message=f"创建变体失败: {str(e)}"
            )
    
    async def update_variant(self, variant_id: int, params: ProductVariantUpdate) -> Result[ProductVariantResponse]:
        """更新变体"""
        try:
            self.logger.info(f"更新变体: id={variant_id}")
            
            # 获取变体
            variant = await self.get_resource_by_id(variant_id)
            if not variant:
                self.logger.warning(f"变体不存在: {variant_id}")
                return self.resource_not_found_result(variant_id)
            
            # 如果更新SKU，检查是否已存在
            if params.sku is not None and params.sku != variant.sku:
                existing_variant = await self.variant_repo.get_by_sku(params.sku)
                if existing_variant and existing_variant.id != variant.id:
                    self.logger.warning(f"变体SKU已存在: {params.sku}")
                    return self.create_error_result(
                        error_code=ErrorCode.ALREADY_EXISTS,
                        error_message=f"变体SKU {params.sku} 已被使用"
                    )
            
            # 更新变体
            update_data = params.model_dump(exclude_unset=True)
            variant = await self.variant_repo.update(variant, data=update_data)
            
            # 构建变体响应
            variant_response = ProductVariantResponse.model_validate(variant.to_dict())
            
            # 触发变体更新事件
            event_data = variant_response.model_dump()
            event_data["updated_fields"] = list(update_data.keys())
            dispatch("products:variant:updated", payload=event_data)
            
            self.logger.info(f"变体更新成功: id={variant.id}, name={variant.name}")
            return self.create_success_result(variant_response)
        except Exception as e:
            self.logger.error(f"更新变体失败: id={variant_id}, 错误={str(e)}", exc_info=True)
            return self.create_error_result(
                error_code=ErrorCode.INTERNAL_ERROR,
                error_message=f"更新变体失败: {str(e)}"
            )
