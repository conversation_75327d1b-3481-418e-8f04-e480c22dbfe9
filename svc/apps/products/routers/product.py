"""
商品API路由
包含商品的创建、查询、更新和删除功能
"""
from typing import Any, List, Optional, Dict

from fastapi import APIRouter, Depends, Query, Path, status

from svc.core.services.result import Result
from svc.apps.auth.dependencies import (
    get_current_active_user,
    has_permission,
    resource_permission
)
from svc.apps.auth.models.user import User
from svc.apps.products.schemas.product import (
    ProductCreate,
    ProductUpdate,
    ProductListResponse,
    GetProductsParams
)
from svc.apps.products.services.product import ProductService
from svc.apps.products.dependencies import get_product_service
from svc.core.exceptions import (
    handle_route_errors,
    PRODUCT_ERROR_MAPPING
)

# 创建路由器
router = APIRouter(
    tags=["商品管理"]
)

# === 管理端路由 (Admin Routes) ===

@router.get("/admin/", response_model=Result[ProductListResponse])
@handle_route_errors(PRODUCT_ERROR_MAPPING)
async def admin_list_products(
    page_num: int = Query(1, ge=1, description="页码"),
    page_size: int = Query(10, ge=1, le=1000, description="每页数量"),
    status: Optional[str] = Query(None, description="商品状态"),
    category_id: Optional[int] = Query(None, description="分类ID"),
    is_featured: Optional[bool] = Query(None, description="是否推荐商品"),
    search_term: Optional[str] = Query(None, description="搜索关键词"),
    min_price: Optional[float] = Query(None, description="最低价格"),
    max_price: Optional[float] = Query(None, description="最高价格"),
    order_by: Optional[str] = Query("created_at", description="排序字段"),
    order_desc: Optional[bool] = Query(True, description="是否降序"),
    product_service: ProductService = Depends(get_product_service),
    current_user: User = Depends(get_current_active_user),
    _: bool = Depends(lambda: has_permission("product:read"))
) -> Result[ProductListResponse]:
    """获取商品列表 (管理端)"""
    params = GetProductsParams(
        page_num=page_num,
        page_size=page_size,
        status=status,
        category_id=category_id,
        is_featured=is_featured,
        search_term=search_term,
        min_price=min_price,
        max_price=max_price,
        order_by=order_by,
        order_desc=order_desc
    )
    result = await product_service.get_products(params=params)
    return result

@router.get("/admin/{product_id}", response_model=Result)
@handle_route_errors(PRODUCT_ERROR_MAPPING)
async def admin_get_product_details(
    product_id: int = Path(..., description="商品ID"),
    product_service: ProductService = Depends(get_product_service),
    current_user: User = Depends(get_current_active_user),
    _: Any = Depends(resource_permission("product", "read")),
) -> Result:
    """获取商品详情 (管理端)"""
    result = await product_service.get_product(product_id=product_id)
    return result

@router.post("/admin/", response_model=Result, status_code=status.HTTP_201_CREATED)
@handle_route_errors(PRODUCT_ERROR_MAPPING)
async def admin_create_product(
    product_data: ProductCreate,
    product_service: ProductService = Depends(get_product_service),
    current_user: User = Depends(get_current_active_user),
    _: bool = Depends(lambda: has_permission("product:create")),
) -> Result:
    """创建商品 (管理端)"""
    result = await product_service.create_product(params=product_data)
    return result

@router.put("/admin/{product_id}", response_model=Result)
@handle_route_errors(PRODUCT_ERROR_MAPPING)
async def admin_update_product(
    product_in: ProductUpdate,
    product_id: int = Path(..., description="商品ID"),
    product_service: ProductService = Depends(get_product_service),
    current_user: User = Depends(get_current_active_user),
    _: Any = Depends(resource_permission("product", "update")),
) -> Result:
    """更新商品 (管理端)"""
    result = await product_service.update_product(product_id=product_id, params=product_in)
    return result

@router.delete("/admin/{product_id}", response_model=Result)
@handle_route_errors(PRODUCT_ERROR_MAPPING)
async def admin_delete_product(
    product_id: int = Path(..., description="商品ID"),
    product_service: ProductService = Depends(get_product_service),
    current_user: User = Depends(get_current_active_user),
    _: Any = Depends(resource_permission("product", "delete")),
) -> Result:
    """删除商品 (管理端)"""
    # 这里应该实现软删除逻辑
    # result = await product_service.delete_product(product_id=product_id)
    # return result
    pass

# === 客户端路由 (Client Routes) ===

@router.get("/", response_model=Result[ProductListResponse])
@handle_route_errors(PRODUCT_ERROR_MAPPING)
async def list_products(
    page_num: int = Query(1, ge=1, description="页码"),
    page_size: int = Query(10, ge=1, le=1000, description="每页数量"),
    category_id: Optional[int] = Query(None, description="分类ID"),
    search_term: Optional[str] = Query(None, description="搜索关键词"),
    min_price: Optional[float] = Query(None, description="最低价格"),
    max_price: Optional[float] = Query(None, description="最高价格"),
    order_by: Optional[str] = Query("created_at", description="排序字段"),
    order_desc: Optional[bool] = Query(True, description="是否降序"),
    product_service: ProductService = Depends(get_product_service),
) -> Result[ProductListResponse]:
    """获取商品列表 (客户端)"""
    params = GetProductsParams(
        page_num=page_num,
        page_size=page_size,
        status="active",  # 客户端只显示上架商品
        category_id=category_id,
        search_term=search_term,
        min_price=min_price,
        max_price=max_price,
        order_by=order_by,
        order_desc=order_desc
    )
    result = await product_service.get_products(params=params)
    return result

@router.get("/featured", response_model=Result[ProductListResponse])
@handle_route_errors(PRODUCT_ERROR_MAPPING)
async def list_featured_products(
    page_num: int = Query(1, ge=1, description="页码"),
    page_size: int = Query(10, ge=1, le=1000, description="每页数量"),
    product_service: ProductService = Depends(get_product_service),
) -> Result[ProductListResponse]:
    """获取推荐商品列表 (客户端)"""
    params = GetProductsParams(
        page_num=page_num,
        page_size=page_size,
        status="active",
        is_featured=True
    )
    result = await product_service.get_products(params=params)
    return result

@router.get("/{product_id}", response_model=Result)
@handle_route_errors(PRODUCT_ERROR_MAPPING)
async def get_product_details(
    product_id: int = Path(..., description="商品ID"),
    product_service: ProductService = Depends(get_product_service),
) -> Result:
    """获取商品详情 (客户端)"""
    result = await product_service.get_product(product_id=product_id)
    return result

@router.get("/category/{category_id}", response_model=Result[ProductListResponse])
@handle_route_errors(PRODUCT_ERROR_MAPPING)
async def list_products_by_category(
    category_id: int = Path(..., description="分类ID"),
    page_num: int = Query(1, ge=1, description="页码"),
    page_size: int = Query(10, ge=1, le=1000, description="每页数量"),
    product_service: ProductService = Depends(get_product_service),
) -> Result[ProductListResponse]:
    """获取指定分类的商品列表 (客户端)"""
    params = GetProductsParams(
        page_num=page_num,
        page_size=page_size,
        status="active",
        category_id=category_id
    )
    result = await product_service.get_products(params=params)
    return result
