import time
import uuid
import logging
from typing import Callable, Awaitable

from fastapi import Request, Response
from starlette.types import ASGIApp

from .base import BaseMiddleware

# 配置日志
logger = logging.getLogger("api")

class RequestLoggingMiddleware(BaseMiddleware):
    """请求日志记录中间件，记录请求和响应信息"""
    
    priority = 10  # 最高优先级，最外层中间件

    __name__ = "request_logging"
    
    def __init__(
        self,
        app: ASGIApp,
        enabled: bool = True,
        exclude_paths: list = None,
        **options
    ):
        super().__init__(app, enabled=enabled, **options)
        self.exclude_paths = exclude_paths or ["/health", "/metrics"]
    
    async def dispatch(
        self, request: Request, call_next: Callable[[Request], Awaitable[Response]]
    ) -> Response:
        """处理请求，记录日志"""
        
        # 如果中间件被禁用，直接处理请求
        if not self.is_enabled():
            return await call_next(request)
        
        # 跳过不需要记录日志的路径
        if any(request.url.path.startswith(path) for path in self.exclude_paths):
            return await call_next(request)
        
        # 生成请求ID
        request_id = str(uuid.uuid4())
        request.state.request_id = request_id
        
        # 记录请求开始时间
        start_time = time.time()
        
        # 记录请求信息
        logger.info(
            f"Request started: {request.method} {request.url.path} "
            f"[ID: {request_id}] [Client: {request.client.host if request.client else 'unknown'}]"
        )
        
        # 处理请求
        try:
            response = await call_next(request)
            
            # 计算处理时间
            process_time = time.time() - start_time
            
            # 记录响应信息
            logger.info(
                f"Request completed: {request.method} {request.url.path} "
                f"[ID: {request_id}] [Status: {response.status_code}] "
                f"[Time: {process_time:.4f}s]"
            )
            
            # 添加请求ID到响应头
            response.headers["X-Request-ID"] = request_id
            
            return response
            
        except Exception as e:
            # 记录异常信息
            process_time = time.time() - start_time
            logger.error(
                f"Request failed: {request.method} {request.url.path} "
                f"[ID: {request_id}] [Time: {process_time:.4f}s] "
                f"[Error: {str(e)}]"
            )
            raise

# 配置日志格式
def setup_logging():
    """配置应用日志"""
    
    # 创建处理器
    console_handler = logging.StreamHandler()
    
    # 设置格式
    formatter = logging.Formatter(
        "%(asctime)s - %(name)s - %(levelname)s - %(message)s"
    )
    console_handler.setFormatter(formatter)
    
    # 配置根日志
    root_logger = logging.getLogger()
    root_logger.setLevel(logging.INFO)
    root_logger.addHandler(console_handler)
    
    # 配置API日志
    api_logger = logging.getLogger("api")
    api_logger.setLevel(logging.INFO)
    
    # 配置SQLAlchemy日志
    sqlalchemy_logger = logging.getLogger("sqlalchemy.engine")
    sqlalchemy_logger.setLevel(logging.WARNING) 