# 商品管理模块

基于市场模块模板创建的商品管理模块，提供完整的商品、分类、变体和库存管理功能。

## 模块结构

```
svc/apps/products/
├── __init__.py                 # 模块初始化
├── README.md                   # 模块说明文档
├── main.py                     # 主路由文件
├── dependencies.py             # 依赖注入
├── models/                     # 数据模型
│   ├── __init__.py
│   ├── product.py             # 商品模型
│   ├── category.py            # 分类模型
│   ├── product_variant.py     # 商品变体模型
│   └── inventory.py           # 库存模型
├── schemas/                    # 请求/响应模式
│   ├── __init__.py
│   ├── product.py             # 商品模式
│   ├── category.py            # 分类模式
│   ├── product_variant.py     # 变体模式
│   └── inventory.py           # 库存模式
├── repositories/               # 数据访问层
│   ├── __init__.py
│   ├── product.py             # 商品仓库
│   ├── category.py            # 分类仓库
│   ├── product_variant.py     # 变体仓库
│   └── inventory.py           # 库存仓库
├── services/                   # 业务逻辑层
│   ├── __init__.py
│   ├── product.py             # 商品服务
│   ├── category.py            # 分类服务
│   ├── product_variant.py     # 变体服务
│   └── inventory.py           # 库存服务
├── routers/                    # API路由
│   ├── __init__.py
│   ├── product.py             # 商品路由
│   ├── category.py            # 分类路由
│   ├── product_variant.py     # 变体路由
│   └── inventory.py           # 库存路由
├── events/                     # 事件处理器
│   ├── __init__.py
│   ├── product_events.py      # 商品事件
│   ├── category_events.py     # 分类事件
│   └── inventory_events.py    # 库存事件
└── utils/                      # 工具函数
    ├── __init__.py
    └── sku_generator.py        # SKU生成器
```

## 核心功能

### 1. 商品管理 (Product)
- 商品的创建、查询、更新、删除
- 商品状态管理（草稿、上架、下架、停产）
- 商品分类关联
- 商品属性和元数据管理
- SEO信息管理
- 商品统计（浏览量、销量、评分）

### 2. 分类管理 (Category)
- 分类的创建、查询、更新、删除
- 层级分类结构支持
- 分类树形结构查询
- 分类状态管理
- SEO信息管理

### 3. 商品变体 (ProductVariant)
- 支持商品的多规格变体（颜色、尺寸等）
- 变体独立的价格和库存
- 变体属性管理
- 默认变体设置

### 4. 库存管理 (Inventory)
- 库存记录的创建和管理
- 库存调整（增加/减少）
- 库存预留和释放
- 批次和过期日期管理
- 仓库位置管理
- 低库存和过期库存监控

## API 端点

### 商品 API
- `GET /products/` - 获取商品列表
- `GET /products/{id}` - 获取商品详情
- `GET /products/featured` - 获取推荐商品
- `GET /products/category/{category_id}` - 获取分类商品
- `POST /products/admin/` - 创建商品（管理端）
- `PUT /products/admin/{id}` - 更新商品（管理端）
- `DELETE /products/admin/{id}` - 删除商品（管理端）

### 分类 API
- `GET /categories/` - 获取分类列表
- `GET /categories/{id}` - 获取分类详情
- `GET /categories/tree` - 获取分类树
- `GET /categories/featured` - 获取推荐分类
- `POST /categories/admin/` - 创建分类（管理端）
- `PUT /categories/admin/{id}` - 更新分类（管理端）

### 变体 API
- `GET /variants/product/{product_id}/variants` - 获取商品变体列表
- `GET /variants/{id}` - 获取变体详情
- `POST /variants/admin/` - 创建变体（管理端）
- `PUT /variants/admin/{id}` - 更新变体（管理端）

### 库存 API
- `GET /inventory/admin/` - 获取库存列表（管理端）
- `GET /inventory/admin/{id}` - 获取库存详情（管理端）
- `POST /inventory/admin/` - 创建库存记录（管理端）
- `POST /inventory/admin/{id}/adjust` - 调整库存（管理端）
- `POST /inventory/admin/{id}/reserve` - 预留库存（管理端）

## 使用示例

### 1. 创建商品

```python
from svc.apps.products.schemas.product import ProductCreate
from svc.apps.products.services.product import ProductService

# 创建商品数据
product_data = ProductCreate(
    name="iPhone 15 Pro",
    description="苹果最新旗舰手机",
    sku="IPHONE15PRO001",
    price=7999.00,
    category_id=1,
    is_featured=True
)

# 创建商品
product_service = ProductService()
result = await product_service.create_product(product_data)
```

### 2. 查询商品列表

```python
from svc.apps.products.schemas.product import GetProductsParams

# 设置查询参数
params = GetProductsParams(
    page_num=1,
    page_size=10,
    status="active",
    category_id=1,
    search_term="iPhone"
)

# 查询商品列表
result = await product_service.get_products(params)
```

### 3. 创建商品变体

```python
from svc.apps.products.schemas.product_variant import ProductVariantCreate

# 创建变体数据
variant_data = ProductVariantCreate(
    product_id=1,
    name="iPhone 15 Pro 钛原色 128GB",
    sku="IPHONE15PRO-TITANIUM-128GB",
    price=7999.00,
    attributes={"color": "钛原色", "storage": "128GB"},
    is_default=True
)

# 创建变体
variant_service = ProductVariantService()
result = await variant_service.create_variant(variant_data)
```

## 事件系统

模块集成了事件系统，支持以下事件：

### 商品事件
- `products:product:created` - 商品创建
- `products:product:updated` - 商品更新
- `products:product:deleted` - 商品删除
- `products:product:status_changed` - 商品状态变更

### 分类事件
- `products:category:created` - 分类创建
- `products:category:updated` - 分类更新
- `products:category:deleted` - 分类删除

### 库存事件
- `products:inventory:created` - 库存创建
- `products:inventory:adjusted` - 库存调整
- `products:inventory:reserved` - 库存预留
- `products:inventory:released` - 库存释放

## 缓存策略

- 商品信息缓存：1小时
- 分类信息缓存：1小时
- 库存信息缓存：30分钟（较短，保证实时性）

## 权限控制

模块集成了基于角色的权限控制：

- `product:read` - 商品查看权限
- `product:create` - 商品创建权限
- `product:update` - 商品更新权限
- `product:delete` - 商品删除权限
- `category:*` - 分类相关权限
- `inventory:*` - 库存相关权限

## 数据库迁移

创建数据库表的迁移脚本：

```sql
-- 创建分类表
CREATE TABLE categories (
    id BIGINT PRIMARY KEY AUTO_INCREMENT,
    name VARCHAR(100) NOT NULL,
    description TEXT,
    slug VARCHAR(100) NOT NULL UNIQUE,
    parent_id BIGINT,
    level INT DEFAULT 0,
    sort_order INT DEFAULT 0,
    status VARCHAR(20) DEFAULT 'active',
    is_featured BOOLEAN DEFAULT FALSE,
    image_url VARCHAR(500),
    icon VARCHAR(100),
    attributes JSON,
    meta_data JSON,
    seo_title VARCHAR(200),
    seo_description TEXT,
    seo_keywords VARCHAR(500),
    product_count INT DEFAULT 0,
    created_at DATETIME NOT NULL,
    updated_at DATETIME NOT NULL,
    FOREIGN KEY (parent_id) REFERENCES categories(id)
);

-- 创建商品表
CREATE TABLE products (
    id BIGINT PRIMARY KEY AUTO_INCREMENT,
    name VARCHAR(200) NOT NULL,
    description TEXT,
    short_description VARCHAR(500),
    sku VARCHAR(100) NOT NULL UNIQUE,
    barcode VARCHAR(100),
    category_id BIGINT,
    price DECIMAL(10,2) NOT NULL DEFAULT 0.00,
    cost_price DECIMAL(10,2),
    market_price DECIMAL(10,2),
    currency VARCHAR(10) DEFAULT 'CNY',
    status VARCHAR(20) DEFAULT 'draft',
    is_featured BOOLEAN DEFAULT FALSE,
    is_digital BOOLEAN DEFAULT FALSE,
    track_inventory BOOLEAN DEFAULT TRUE,
    stock_quantity INT DEFAULT 0,
    min_stock_level INT DEFAULT 0,
    max_stock_level INT,
    weight DECIMAL(8,3),
    dimensions JSON,
    attributes JSON,
    meta_data JSON,
    seo_title VARCHAR(200),
    seo_description TEXT,
    seo_keywords VARCHAR(500),
    view_count INT DEFAULT 0,
    sales_count INT DEFAULT 0,
    rating_average DECIMAL(3,2) DEFAULT 0.00,
    rating_count INT DEFAULT 0,
    created_at DATETIME NOT NULL,
    updated_at DATETIME NOT NULL,
    FOREIGN KEY (category_id) REFERENCES categories(id)
);

-- 其他表的创建脚本类似...
```

## 注意事项

1. **SKU唯一性**：确保商品和变体的SKU在系统中唯一
2. **库存一致性**：库存操作需要考虑并发安全
3. **分类层级**：分类删除时需要处理子分类和商品
4. **缓存更新**：数据变更时及时更新缓存
5. **事件处理**：确保事件处理器的异常不影响主业务流程
