"""数据库迁移环境配置。

该模块提供Alembic迁移工具的环境配置，包括：
- 数据库连接配置
- 迁移上下文设置
- 在线和离线迁移支持
- 日志配置
"""

# 标准库导入
import asyncio
import os
from logging.config import fileConfig

# 第三方库导入
from alembic import context
from sqlalchemy import engine_from_config, pool
from sqlalchemy.engine import Connection
from sqlalchemy.ext.asyncio import AsyncEngine

# 项目内部导入
from svc.core.config.settings import get_settings
from svc.core.database.session import Base

# Alembic配置对象，提供对.ini文件中配置值的访问
config = context.config

# 从配置中获取数据库连接信息
settings = get_settings()
section = config.config_ini_section

# 设置数据库连接参数
db_config = {
    "POSTGRES_SERVER": settings.db_server,
    "POSTGRES_USER": settings.db_user,
    "POSTGRES_PASSWORD": settings.db_password,
    "POSTGRES_DB": settings.db_name,
}

# 更新配置节
for key, value in db_config.items():
    config.set_section_option(section, key, value)

# 配置Python日志
if config.config_file_name is not None:
    fileConfig(config.config_file_name)

# 设置模型的元数据对象，用于支持'autogenerate'
target_metadata = Base.metadata

def run_migrations_offline() -> None:
    """以'离线'模式运行迁移。
    
    该模式下只需要一个数据库URL，不需要创建Engine对象。
    适用于不能直接访问数据库的环境。
    
    注意：
    - 该模式下的迁移会生成SQL脚本
    - 不需要DBAPI驱动
    - context.execute()会将SQL语句输出到脚本
    """
    url = config.get_main_option("sqlalchemy.url")
    context.configure(
        url=url,
        target_metadata=target_metadata,
        literal_binds=True,
        dialect_opts={"paramstyle": "named"},
    )

    with context.begin_transaction():
        context.run_migrations()

def do_run_migrations(connection: Connection) -> None:
    """执行实际的数据库迁移。
    
    Args:
        connection: 数据库连接对象
    """
    context.configure(connection=connection, target_metadata=target_metadata)

    with context.begin_transaction():
        context.run_migrations()

async def run_migrations_online() -> None:
    """以'在线'模式运行迁移。
    
    该模式下会创建一个Engine对象并与上下文关联一个连接。
    这是推荐的迁移方式。
    
    注意：
    - 使用异步连接池
    - 自动处理连接的获取和释放
    - 支持事务和回滚
    """
    connectable = AsyncEngine(
        engine_from_config(
            config.get_section(config.config_ini_section),
            prefix="sqlalchemy.",
            poolclass=pool.NullPool,
            future=True,
        )
    )

    async with connectable.connect() as connection:
        await connection.run_sync(do_run_migrations)

    await connectable.dispose()

# 根据运行模式选择迁移方式
if context.is_offline_mode():
    run_migrations_offline()
else:
    asyncio.run(run_migrations_online()) 