"""
系统服务实现。
提供系统级功能的业务逻辑实现，协调审计日志和系统监控功能。
"""

import logging
from datetime import datetime
from typing import Any, Dict, List, Optional

from fastapi import Request
from redis.asyncio import Redis
from sqlalchemy.ext.asyncio import AsyncSession

from svc.apps.system.services.audit_log import AuditLogService
from svc.apps.system.services.monitor import SystemMonitorService
from svc.core.exceptions.error_codes import ErrorCode
from svc.core.services import BaseService
from svc.core.services.result import Result, ResultFactory


class SystemService(BaseService[None, Result[Dict[str, Any]]]):
    """
    系统服务类，协调审计日志和系统监控功能
    
    主要功能：
    - 提供统一的系统服务接口
    - 协调审计日志服务和监控服务
    - 提供系统级别的操作和查询
    """
    
    resource_type = "system"
    
    def __init__(
        self, 
        db: AsyncSession, 
        redis: Optional[Redis] = None,
        audit_log_service: Optional[AuditLogService] = None,
        monitor_service: Optional[SystemMonitorService] = None
    ):
        """初始化系统服务
        
        Args:
            db: 数据库会话
            redis: Redis客户端（可选）
            audit_log_service: 审计日志服务实例，不提供则创建新实例
            monitor_service: 系统监控服务实例，不提供则创建新实例
        """
        super().__init__(db, redis)
        self.logger = logging.getLogger(__name__)
        self.audit_log_service = audit_log_service or AuditLogService(db, redis)
        self.monitor_service = monitor_service or SystemMonitorService(db, redis)
    
    # 系统监控相关方法
    
    async def get_system_info(self) -> Result[Dict[str, Any]]:
        """
        获取系统信息
        
        Returns:
            Result[Dict[str, Any]]: 系统信息
        """
        return await self.monitor_service.get_system_info()
    
    async def get_health_status(self) -> Result[Dict[str, Any]]:
        """
        获取系统健康状态
        
        Returns:
            Result[Dict[str, Any]]: 健康状态信息
        """
        return await self.monitor_service.get_health_status()
    
    async def get_performance_metrics(self) -> Result[Dict[str, Any]]:
        """
        获取性能指标
        
        Returns:
            Result[Dict[str, Any]]: 性能指标
        """
        return await self.monitor_service.get_performance_metrics()
    
    # 审计日志相关方法
    
    async def log_action(
        self,
        action: str,
        resource_type: str,
        resource_id: Optional[str] = None,
        user_id: Optional[int] = None,
        username: Optional[str] = None,
        request: Optional[Request] = None,
        details: Optional[Dict[str, Any]] = None,
        status: str = "success",
        message: Optional[str] = None
    ) -> Result[Dict[str, Any]]:
        """
        记录操作日志
        
        Args:
            action: 操作类型
            resource_type: 资源类型
            resource_id: 资源ID
            user_id: 执行操作的用户ID
            username: 执行操作的用户名
            request: 请求对象，用于获取IP和User-Agent
            details: 操作详情
            status: 操作结果
            message: 额外信息
            
        Returns:
            Result[Dict[str, Any]]: 创建的审计日志
        """
        return await self.audit_log_service.create_audit_log(
            action=action,
            resource_type=resource_type,
            resource_id=resource_id,
            user_id=user_id,
            username=username,
            request=request,
            details=details,
            status=status,
            message=message
        )
    
    async def get_audit_log(self, log_id: int) -> Result[Dict[str, Any]]:
        """
        获取审计日志详情
        
        Args:
            log_id: 审计日志ID
            
        Returns:
            Result[Dict[str, Any]]: 审计日志详情
        """
        return await self.audit_log_service.get_log(log_id)
    
    async def search_audit_logs(
        self,
        action: Optional[str] = None,
        resource_type: Optional[str] = None,
        resource_id: Optional[str] = None,
        user_id: Optional[int] = None,
        username: Optional[str] = None,
        status: Optional[str] = None,
        start_date: Optional[datetime] = None,
        end_date: Optional[datetime] = None,
        page: int = 1,
        page_size: int = 20
    ) -> Result[Dict[str, Any]]:
        """
        搜索审计日志
        
        Args:
            action: 操作类型
            resource_type: 资源类型
            resource_id: 资源ID
            user_id: 用户ID
            username: 用户名
            status: 操作结果
            start_date: 开始日期
            end_date: 结束日期
            page: 页码，从1开始
            page_size: 每页数量
            
        Returns:
            Result[Dict[str, Any]]: 审计日志列表和分页信息
        """
        return await self.audit_log_service.search_logs(
            action=action,
            resource_type=resource_type,
            resource_id=resource_id,
            user_id=user_id,
            username=username,
            status=status,
            start_date=start_date,
            end_date=end_date,
            page=page,
            page_size=page_size
        )
    
    async def get_latest_audit_logs(self, limit: int = 10) -> Result[List[Dict[str, Any]]]:
        """
        获取最新的审计日志
        
        Args:
            limit: 返回数量限制
            
        Returns:
            Result[List[Dict[str, Any]]]: 审计日志列表
        """
        return await self.audit_log_service.get_latest_logs(limit)
    
    async def get_resource_audit_logs(
        self, 
        resource_type: str, 
        resource_id: str
    ) -> Result[List[Dict[str, Any]]]:
        """
        获取资源的审计日志
        
        Args:
            resource_type: 资源类型
            resource_id: 资源ID
            
        Returns:
            Result[List[Dict[str, Any]]]: 审计日志列表
        """
        return await self.audit_log_service.get_resource_logs(resource_type, resource_id)
    
    async def get_user_audit_logs(
        self, 
        user_id: int, 
        limit: int = 50
    ) -> Result[List[Dict[str, Any]]]:
        """
        获取用户的操作日志
        
        Args:
            user_id: 用户ID
            limit: 返回数量限制
            
        Returns:
            Result[List[Dict[str, Any]]]: 审计日志列表
        """
        return await self.audit_log_service.get_user_logs(user_id, limit)
    
    async def get_audit_statistics(
        self,
        start_date: Optional[datetime] = None,
        end_date: Optional[datetime] = None
    ) -> Result[Dict[str, Any]]:
        """
        获取审计日志统计信息
        
        Args:
            start_date: 开始日期
            end_date: 结束日期
            
        Returns:
            Result[Dict[str, Any]]: 统计信息
        """
        return await self.audit_log_service.get_statistics(start_date, end_date)
    
    # 便捷方法
    
    async def log_login(
        self, 
        user_id: int, 
        username: str, 
        request: Optional[Request] = None,
        status: str = "success"
    ) -> Result[Dict[str, Any]]:
        """记录用户登录日志"""
        return await self.audit_log_service.log_login(user_id, username, request, status)
    
    async def log_logout(
        self, 
        user_id: int, 
        username: str, 
        request: Optional[Request] = None
    ) -> Result[Dict[str, Any]]:
        """记录用户登出日志"""
        return await self.audit_log_service.log_logout(user_id, username, request)
    
    async def log_data_access(
        self,
        resource_type: str,
        resource_id: str,
        user_id: int,
        username: str,
        action: str = "read",
        request: Optional[Request] = None
    ) -> Result[Dict[str, Any]]:
        """记录数据访问日志"""
        return await self.audit_log_service.log_data_access(
            resource_type, resource_id, user_id, username, action, request
        )
