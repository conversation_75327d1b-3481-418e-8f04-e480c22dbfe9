"""
产品工具类测试脚本
用于验证PriceCalculator、InventoryCalculator和ProductValidator的功能
"""

import os
import sys

# 添加项目根目录到Python路径
sys.path.append(os.path.join(os.path.dirname(__file__), '../../../..'))

from svc.apps.products.utils.inventory_calculator import InventoryCalculator
from svc.apps.products.utils.price_calculator import PriceCalculator
from svc.apps.products.utils.product_validator import ProductValidator
from svc.apps.products.utils.sku_generator import SKUGenerator


def test_price_calculator():
    """测试价格计算器"""
    print("=== 测试价格计算器 ===")
    
    calculator = PriceCalculator()
    
    # 测试折扣计算
    discount_result = calculator.calculate_discount_price(
        original_price=100.00,
        discount_type='percentage',
        discount_value=20.0
    )
    print(f"折扣计算结果: {discount_result}")
    
    # 测试批量价格
    bulk_rules = [
        {'min_qty': 10, 'discount_type': 'percentage', 'discount_value': 5},
        {'min_qty': 50, 'discount_type': 'percentage', 'discount_value': 10},
        {'min_qty': 100, 'discount_type': 'percentage', 'discount_value': 15}
    ]
    
    bulk_result = calculator.calculate_bulk_price(
        unit_price=50.00,
        quantity=25,
        bulk_rules=bulk_rules
    )
    print(f"批量价格计算结果: {bulk_result}")
    
    # 测试税费计算
    tax_result = calculator.calculate_tax(
        price=100.00,
        tax_rate=13.0,
        tax_inclusive=False
    )
    print(f"税费计算结果: {tax_result}")
    
    # 测试利润率计算
    profit_result = calculator.calculate_profit_margin(
        selling_price=150.00,
        cost_price=100.00
    )
    print(f"利润率计算结果: {profit_result}")


def test_inventory_calculator():
    """测试库存计算器"""
    print("\n=== 测试库存计算器 ===")
    
    calculator = InventoryCalculator()
    
    # 模拟库存数据
    mock_inventories = [
        {
            'quantity': 100,
            'reserved_quantity': 10,
            'available_quantity': 90,
            'status': 'available',
            'warehouse_location': 'A区',
            'unit_cost': 5000,  # 50.00元，以分为单位
            'is_available': lambda: True,
            'is_expired': lambda: False
        },
        {
            'quantity': 50,
            'reserved_quantity': 5,
            'available_quantity': 45,
            'status': 'available',
            'warehouse_location': 'B区',
            'unit_cost': 4800,  # 48.00元
            'is_available': lambda: True,
            'is_expired': lambda: False
        }
    ]
    
    # 测试可用库存计算
    available_result = calculator.calculate_available_stock(mock_inventories)
    print(f"可用库存计算结果: {available_result}")
    
    # 测试安全库存计算
    safety_result = calculator.calculate_safety_stock(
        average_demand=10.0,
        lead_time_days=7,
        demand_variability=0.2,
        service_level=0.95
    )
    print(f"安全库存计算结果: {safety_result}")
    
    # 测试库存周转率
    turnover_result = calculator.calculate_turnover_rate(
        cost_of_goods_sold=120000.0,
        average_inventory_value=20000.0,
        period_days=365
    )
    print(f"库存周转率计算结果: {turnover_result}")
    
    # 测试库存预测
    historical_sales = [
        {'date': '2023-01-01', 'quantity': 8},
        {'date': '2023-01-02', 'quantity': 12},
        {'date': '2023-01-03', 'quantity': 10},
        {'date': '2023-01-04', 'quantity': 15},
        {'date': '2023-01-05', 'quantity': 9},
        {'date': '2023-01-06', 'quantity': 11},
        {'date': '2023-01-07', 'quantity': 13}
    ]
    
    prediction_result = calculator.predict_stock_needs(
        historical_sales=historical_sales,
        forecast_days=30,
        growth_rate=0.1
    )
    print(f"库存预测结果: {prediction_result}")
    
    # 测试缺货风险评估
    risk_result = calculator.assess_stockout_risk(
        current_stock=50,
        daily_demand=8.0,
        lead_time_days=7,
        safety_stock=20
    )
    print(f"缺货风险评估结果: {risk_result}")


def test_product_validator():
    """测试产品验证器"""
    print("\n=== 测试产品验证器 ===")
    
    validator = ProductValidator()
    
    # 测试产品数据验证
    product_data = {
        'name': '测试商品',
        'sku': 'PRD123456',
        'price': 99.99,
        'cost_price': 60.00,
        'market_price': 120.00,
        'stock_quantity': 100,
        'min_stock_level': 10,
        'max_stock_level': 500,
        'track_inventory': True,
        'status': 'active',
        'barcode': '1234567890123'
    }
    
    validation_result = validator.validate_product(product_data, strict_mode=True)
    print(f"产品验证结果: {validation_result}")
    
    # 测试变体数据验证
    variant_data = {
        'name': '红色-大号',
        'sku': 'PRD123456-RED-L',
        'product_id': 1,
        'price': 109.99,
        'stock_quantity': 25
    }
    
    variant_validation = validator.validate_product_variant(variant_data, product_data)
    print(f"变体验证结果: {variant_validation}")
    
    # 测试库存一致性验证
    mock_inventories = [
        {
            'quantity': 60,
            'available_quantity': 55
        },
        {
            'quantity': 40,
            'available_quantity': 35
        }
    ]
    
    consistency_result = validator.validate_inventory_consistency(
        product_data,
        mock_inventories
    )
    print(f"库存一致性验证结果: {consistency_result}")


def test_sku_generator():
    """测试SKU生成器"""
    print("\n=== 测试SKU生成器 ===")
    
    # 测试时间戳SKU
    timestamp_sku = SKUGenerator.generate_timestamp_sku("PRD")
    print(f"时间戳SKU: {timestamp_sku}")
    
    # 测试分类SKU
    category_sku = SKUGenerator.generate_category_sku("ELEC", 123, "PRD")
    print(f"分类SKU: {category_sku}")
    
    # 测试属性SKU
    attributes = {"color": "red", "size": "L"}
    attribute_sku = SKUGenerator.generate_attribute_sku("PRD123456", attributes)
    print(f"属性SKU: {attribute_sku}")
    
    # 测试随机SKU
    random_sku = SKUGenerator.generate_random_sku(12, "PRD")
    print(f"随机SKU: {random_sku}")
    
    # 测试SKU验证
    valid_sku = SKUGenerator.validate_sku("PRD123456")
    invalid_sku = SKUGenerator.validate_sku("PRD-")
    print(f"SKU验证 - 有效: {valid_sku}, 无效: {invalid_sku}")


def test_integration():
    """集成测试"""
    print("\n=== 集成测试 ===")
    
    # 创建一个完整的产品测试流程
    
    # 1. 生成SKU
    sku = SKUGenerator.generate_category_sku("ELEC", 1, "PRD")
    print(f"生成的SKU: {sku}")
    
    # 2. 创建产品数据
    product_data = {
        'name': '智能手机',
        'sku': sku,
        'price': 2999.00,
        'cost_price': 2000.00,
        'market_price': 3299.00,
        'stock_quantity': 50,
        'min_stock_level': 5,
        'max_stock_level': 200,
        'track_inventory': True,
        'status': 'active'
    }
    
    # 3. 验证产品数据
    validator = ProductValidator()
    validation = validator.validate_product(product_data, strict_mode=True)
    print(f"产品验证: {'通过' if validation['valid'] else '失败'}")
    if not validation['valid']:
        print(f"错误: {validation['errors']}")
    
    # 4. 计算价格信息
    calculator = PriceCalculator()
    profit = calculator.calculate_profit_margin(
        product_data['price'],
        product_data['cost_price']
    )
    print(f"利润率: {profit['profit_margin']}%")
    
    # 5. 评估库存状况
    inventory_calc = InventoryCalculator()
    risk = inventory_calc.assess_stockout_risk(
        current_stock=product_data['stock_quantity'],
        daily_demand=2.0,
        lead_time_days=10,
        safety_stock=product_data['min_stock_level']
    )
    print(f"库存风险等级: {risk['risk_level']}")
    print(f"库存可用天数: {risk['days_of_stock']}")


if __name__ == "__main__":
    test_price_calculator()
    test_inventory_calculator()
    test_product_validator()
    test_sku_generator()
    test_integration()
    print("\n=== 所有测试完成 ===")
