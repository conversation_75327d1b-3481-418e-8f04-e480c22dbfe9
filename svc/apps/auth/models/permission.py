"""权限模块的ORM模型定义。

提供权限相关的数据库模型，包括：
- 权限基本信息
- 权限与角色关系
"""

# 标准库导入
from typing import Optional, List
from datetime import datetime

# 第三方库导入
from sqlalchemy import Column, String, DateTime, Integer, Table, ForeignKey
from sqlalchemy.orm import relationship, Mapped, mapped_column

# 项目内部导入
from svc.core.database.session import Base

# 角色-权限关联表
role_permission_table = Table(
    "role_permissions",
    Base.metadata,
    Column("role_id", Integer, ForeignKey("roles.id", ondelete="CASCADE")),
    Column("permission_id", Integer, ForeignKey("permissions.id", ondelete="CASCADE"))
)

class Permission(Base):
    """权限模型"""
    
    __tablename__ = "permissions"
    
    id: Mapped[int] = mapped_column(Integer, primary_key=True, autoincrement=True)
    name: Mapped[str] = mapped_column(String(100), unique=True, index=True)
    description: Mapped[Optional[str]] = mapped_column(String(255))
    
    # 关联关系
    roles: Mapped[List["Role"]] = relationship(
        "Role",
        secondary=role_permission_table,
        back_populates="permissions"
    )
    
    def __repr__(self) -> str:
        return f"<Permission {self.name}>" 