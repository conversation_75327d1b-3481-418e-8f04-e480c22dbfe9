"""
订阅计划管理API路由
包含订阅计划的创建、查询、更新和删除功能
"""
from typing import Any, List, Optional

from fastapi import APIRouter, Depends, Query, Path


from svc.core.services.result import Result
from svc.apps.auth.dependencies import (
    get_current_active_user, 
    has_permission,
    resource_permission
)
from svc.apps.auth.models.user import User
from svc.apps.billing.schemas.subscription_plan import (
    GetPlanParams,
    SubscriptionPlanCreate,
    SubscriptionPlanUpdate,
    GetPlansParams,
    CreatePlanParams,
    UpdatePlanParams,
    DeletePlanParams
)
from svc.apps.billing.services.subscription_plan import SubscriptionPlanService
from svc.apps.billing.dependencies import get_subscription_plan_service
from svc.core.exceptions import (
    handle_route_errors,
    PLAN_ERROR_MAPPING
)


# Define the router for this file (prefix will be added by factory)
router = APIRouter(tags=["订阅计划"])

# === 客户端/公共路由 (Client/Public Routes) ===

@router.get("/list", response_model=Result) # Path: /subscription_plans/list
@handle_route_errors(PLAN_ERROR_MAPPING)
async def list_subscription_plans(
    page_num: int = Query(1, ge=1, description="页码"),
    page_size: int = Query(10, ge=1, le=1000, description="每页数量"),
    active_only: Optional[bool] = Query(False, description="是否只返回激活的计划"),
    subscription_plan_service: SubscriptionPlanService = Depends(get_subscription_plan_service),
) -> Result:
    """获取订阅计划列表 (公开)"""
    params = GetPlansParams(
        page_num=page_num,
        page_size=page_size,
        active_only=active_only
    )
    result = await subscription_plan_service.get_plans(params)
    return result

@router.get("/active", response_model=Result) # Path: /subscription_plans/active
@handle_route_errors(PLAN_ERROR_MAPPING)
async def get_active_subscription_plans(
    page_num: int = Query(1, ge=1, description="页码"),
    page_size: int = Query(10, ge=1, le=1000, description="每页数量"),
    subscription_plan_service: SubscriptionPlanService = Depends(get_subscription_plan_service),
) -> Result:
    """获取所有活跃的订阅计划 (公开)"""
    params = GetPlansParams(
        page_num=page_num,
        page_size=page_size,
        active_only=True
    )
    result = await subscription_plan_service.get_plans(params)
    return result

@router.get("/details/{plan_id}", response_model=Result) # Path: /subscription_plans/details/{plan_id}
@handle_route_errors(PLAN_ERROR_MAPPING)
async def get_subscription_plan(
    plan_id: int = Path(..., description="计划ID"),
    subscription_plan_service: SubscriptionPlanService = Depends(get_subscription_plan_service),
) -> Result:
    """获取订阅计划详情 (公开)"""
    params = GetPlanParams(plan_id=plan_id)
    result = await subscription_plan_service.get_plan(params)
    return result

# === 管理端路由 (Admin Routes) ===

@router.post("/admin/", response_model=Result) # Path: /subscription_plans/admin/
@handle_route_errors(PLAN_ERROR_MAPPING)
async def admin_create_subscription_plan(
    plan_data: SubscriptionPlanCreate,
    subscription_plan_service: SubscriptionPlanService = Depends(get_subscription_plan_service),
    current_user: User = Depends(get_current_active_user),
    _: bool = Depends(lambda: has_permission("subscription_plan:create")),
) -> Result:
    """创建订阅计划 (管理端)"""
    params = CreatePlanParams(plan_data=plan_data)
    result = await subscription_plan_service.create_plan(params)
    return result

@router.put("/admin/{plan_id}", response_model=Result) # Path: /subscription_plans/admin/{plan_id}
@handle_route_errors(PLAN_ERROR_MAPPING)
async def admin_update_subscription_plan(
    plan_data: SubscriptionPlanUpdate,
    plan_id: int = Path(..., description="计划ID"),
    subscription_plan_service: SubscriptionPlanService = Depends(get_subscription_plan_service),
    current_user: User = Depends(get_current_active_user),
    _: Any = Depends(resource_permission("subscription_plan", "update")),
) -> Result:
    """更新订阅计划 (管理端)"""
    params = UpdatePlanParams(
        plan_id=plan_id,
        plan_data=plan_data
    )
    result = await subscription_plan_service.update_plan(params)
    return result

@router.delete("/admin/{plan_id}", response_model=Result) # Path: /subscription_plans/admin/{plan_id}
@handle_route_errors(PLAN_ERROR_MAPPING)
async def admin_delete_subscription_plan(
    plan_id: int = Path(..., description="计划ID"),
    subscription_plan_service: SubscriptionPlanService = Depends(get_subscription_plan_service),
    current_user: User = Depends(get_current_active_user),
    _: Any = Depends(resource_permission("subscription_plan", "delete")),
) -> Result:
    """删除订阅计划 (管理端)"""
    params = DeletePlanParams(plan_id=plan_id)
    result = await subscription_plan_service.delete_plan(params)
    return result

@router.post("/admin/activate/{plan_id}", response_model=Result) # Path: /subscription_plans/admin/activate/{plan_id}
@handle_route_errors(PLAN_ERROR_MAPPING)
async def admin_activate_subscription_plan(
    plan_id: int = Path(..., description="计划ID"),
    subscription_plan_service: SubscriptionPlanService = Depends(get_subscription_plan_service),
    current_user: User = Depends(get_current_active_user),
    _: Any = Depends(resource_permission("subscription_plan", "update")),
) -> Result:
    """激活订阅计划 (管理端)"""
    # Service logic might need adjustment if it expects update data differently
    result = await subscription_plan_service.activate_plan(plan_id=plan_id)
    return result

@router.post("/admin/deactivate/{plan_id}", response_model=Result) # Path: /subscription_plans/admin/deactivate/{plan_id}
@handle_route_errors(PLAN_ERROR_MAPPING)
async def admin_deactivate_subscription_plan(
    plan_id: int = Path(..., description="计划ID"),
    subscription_plan_service: SubscriptionPlanService = Depends(get_subscription_plan_service),
    current_user: User = Depends(get_current_active_user),
    _: Any = Depends(resource_permission("subscription_plan", "update")),
) -> Result:
    """停用订阅计划 (管理端)"""
    # Service logic might need adjustment
    result = await subscription_plan_service.deactivate_plan(plan_id=plan_id)
    return result