"""
商品管理模块数据访问层

该模块包含商品管理相关的仓库类定义：
- ProductRepository: 商品数据访问
- CategoryRepository: 分类数据访问
- ProductVariantRepository: 商品变体数据访问
- InventoryRepository: 库存数据访问
"""

from .product import ProductRepository
from .category import CategoryRepository
from .product_variant import ProductVariantRepository
from .inventory import InventoryRepository

__all__ = [
    "ProductRepository",
    "CategoryRepository", 
    "ProductVariantRepository",
    "InventoryRepository"
]
