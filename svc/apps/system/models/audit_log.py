"""
审计日志数据模型。
用于记录系统中的重要操作，支持安全审计和合规要求。
"""

from datetime import datetime, timezone
from typing import Any, Dict, Optional

from sqlalchemy import JSON, Column, DateTime, Integer, String, Text
from sqlalchemy.orm import Mapped

from svc.core.database.session import Base


class AuditLog(Base):
    """
    审计日志模型
    
    用于记录系统中的重要操作，如用户登录、数据修改、配置变更等。
    支持安全审计和合规要求，记录谁在什么时间做了什么操作。
    """
    __tablename__ = "audit_logs"
    
    id: Mapped[int] = Column(Integer, primary_key=True, index=True, autoincrement=True)
    # 操作类型（如 login, create, update, delete 等）
    action: Mapped[str] = Column(String(50), nullable=False, index=True)
    # 操作资源类型（如 user, role, config 等）
    resource_type: Mapped[str] = Column(String(50), nullable=False, index=True)
    # 操作资源ID
    resource_id: Mapped[Optional[str]] = Column(String(50), nullable=True, index=True)
    # 执行操作的用户ID
    user_id: Mapped[Optional[int]] = Column(Integer, nullable=True, index=True)
    # 用户名，冗余存储，便于查询
    username: Mapped[Optional[str]] = Column(String(50), nullable=True)
    # IP地址
    ip_address: Mapped[Optional[str]] = Column(String(50), nullable=True)
    # 用户代理（浏览器或客户端信息）
    user_agent: Mapped[Optional[str]] = Column(String(200), nullable=True)
    # 操作结果（成功/失败）
    status: Mapped[str] = Column(String(20), nullable=False, default="success")
    # 操作详情（存储为JSON）
    details: Mapped[Optional[Dict[str, Any]]] = Column(JSON, nullable=True)
    # 额外信息
    message: Mapped[Optional[str]] = Column(Text, nullable=True)
    # 创建时间（操作时间）
    created_at: Mapped[datetime] = Column(DateTime, nullable=False, default=lambda: datetime.now(timezone.utc))
    
    def __repr__(self) -> str:
        """字符串表示"""
        return (
            f"<AuditLog(id={self.id}, action='{self.action}', "
            f"resource_type='{self.resource_type}', resource_id='{self.resource_id}', "
            f"user_id={self.user_id}, status='{self.status}', created_at={self.created_at})>"
        )
    
    def to_dict(self) -> Dict[str, Any]:
        """转换为字典"""
        return {
            "id": self.id,
            "action": self.action,
            "resource_type": self.resource_type,
            "resource_id": self.resource_id,
            "user_id": self.user_id,
            "username": self.username,
            "ip_address": self.ip_address,
            "user_agent": self.user_agent,
            "status": self.status,
            "details": self.details,
            "message": self.message,
            "created_at": self.created_at.isoformat() if self.created_at else None
        } 