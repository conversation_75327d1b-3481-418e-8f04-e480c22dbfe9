from typing import Optional

from redis.asyncio import Redis

from svc.apps.products.repositories.inventory import InventoryRepository
from svc.core.exceptions.error_codes import ErrorCode
from svc.core.services import BaseService
from svc.core.services.result import Result
from svc.apps.products.models.inventory import Inventory
from svc.apps.products.schemas.inventory import (
    InventoryResponse,
    InventoryCreate,
    InventoryUpdate,
    InventoryListResponse,
    GetInventoriesParams,
    InventoryAdjustmentRequest,
    InventoryReservationRequest
)
from fastapi_events.dispatcher import dispatch

# 缓存配置
CACHE_TTL = 1800  # 库存缓存时间较短（30分钟）

class InventoryService(BaseService[Inventory, Result[InventoryResponse]]):
    """库存服务类，提供库存的创建、查询和管理功能"""
    
    # 设置资源类型名称
    resource_type = "inventory"
    
    def __init__(
        self, 
        redis: Optional[Redis] = None, 
        inventory_repo: Optional[InventoryRepository] = None
    ):
        """初始化库存服务"""
        super().__init__(redis)
        self.inventory_repo = inventory_repo
    
    async def get_resource_by_id(self, inventory_id: int) -> Optional[Inventory]:
        """获取指定ID的库存资源"""
        return await self.inventory_repo.get_by_id(inventory_id)
    
    async def get_inventory(self, inventory_id: int) -> Result[InventoryResponse]:
        """获取库存信息"""
        try:
            self.logger.info(f"获取库存: id={inventory_id}")
            
            # 查询数据库
            inventory = await self.get_resource_by_id(inventory_id)
            if not inventory:
                self.logger.warning(f"库存不存在: id={inventory_id}")
                return self.resource_not_found_result(inventory_id)
            
            # 构建库存响应
            inventory_response = InventoryResponse.model_validate(inventory.to_dict())
            
            return self.create_success_result(inventory_response)
        except Exception as e:
            self.logger.error(f"获取库存失败: id={inventory_id}, 错误={str(e)}", exc_info=True)
            return self.create_error_result(
                error_code=ErrorCode.NOT_FOUND,
                error_message=f"获取库存失败: {str(e)}"
            )
    
    async def get_inventories(self, params: GetInventoriesParams) -> Result[InventoryListResponse]:
        """获取库存列表"""
        try:
            page_num = params.page_num
            page_size = params.page_size
            skip = (page_num - 1) * page_size
            self.logger.info(f"获取库存列表: page={page_num}, size={page_size}")
            
            # 使用仓库类获取库存列表和总数
            inventories, total = await self.inventory_repo.get_inventories(
                skip=skip,
                limit=page_size,
                product_id=params.product_id,
                variant_id=params.variant_id,
                status=params.status,
                warehouse_location=params.warehouse_location,
                supplier_id=params.supplier_id,
                low_stock_only=params.low_stock_only,
                expired_only=params.expired_only,
                order_by=params.order_by,
                order_desc=params.order_desc
            )
            
            # 构建库存响应列表
            inventory_responses = []
            for inventory in inventories:
                inventory_response = InventoryResponse.model_validate(inventory.to_dict())
                inventory_responses.append(inventory_response)
                
            # 计算总页数
            total_pages = (total + page_size - 1) // page_size if page_size > 0 else 0
            
            # 构建分页响应
            paginated_response = InventoryListResponse(
                items=inventory_responses,
                total=total,
                page=page_num,
                size=page_size,
                pages=total_pages
            )
            
            return self.create_success_result(paginated_response)
        except Exception as e:
            self.logger.error(f"获取库存列表失败: 错误={str(e)}", exc_info=True)
            return self.create_error_result(
                error_code=ErrorCode.OPERATION_FAILED,
                error_message=f"获取库存列表失败: {str(e)}"
            )
    
    async def create_inventory(self, params: InventoryCreate) -> Result[InventoryResponse]:
        """创建库存记录"""
        try:
            self.logger.info(f"创建库存: product_id={params.product_id}, quantity={params.quantity}")
            
            # 创建库存
            inventory = await self.inventory_repo.create(**params.model_dump())
            
            # 构建库存响应
            inventory_response = InventoryResponse.model_validate(inventory.to_dict())
            
            # 触发库存创建事件
            event_data = inventory_response.model_dump()
            dispatch("products:inventory:created", payload=event_data)
            
            self.logger.info(f"库存创建成功: id={inventory.id}")
            return self.create_success_result(inventory_response)
        except Exception as e:
            self.logger.error(f"创建库存失败: 错误={str(e)}", exc_info=True)
            return self.create_error_result(
                error_code=ErrorCode.OPERATION_FAILED,
                error_message=f"创建库存失败: {str(e)}"
            )
    
    async def adjust_inventory(
        self, 
        inventory_id: int, 
        params: InventoryAdjustmentRequest
    ) -> Result[InventoryResponse]:
        """调整库存数量"""
        try:
            self.logger.info(f"调整库存: id={inventory_id}, type={params.adjustment_type}, quantity={params.quantity}")
            
            # 获取库存
            inventory = await self.get_resource_by_id(inventory_id)
            if not inventory:
                self.logger.warning(f"库存不存在: {inventory_id}")
                return self.resource_not_found_result(inventory_id)
            
            # 计算调整量
            quantity_change = params.quantity if params.adjustment_type == "increase" else -params.quantity
            
            # 调整库存
            inventory = await self.inventory_repo.adjust_inventory(
                inventory_id, 
                quantity_change, 
                params.reason
            )
            
            if not inventory:
                return self.create_error_result(
                    error_code=ErrorCode.OPERATION_FAILED,
                    error_message="库存调整失败"
                )
            
            # 构建库存响应
            inventory_response = InventoryResponse.model_validate(inventory.to_dict())
            
            # 触发库存调整事件
            event_data = inventory_response.model_dump()
            event_data.update({
                "adjustment_type": params.adjustment_type,
                "adjustment_quantity": params.quantity,
                "reason": params.reason
            })
            dispatch("products:inventory:adjusted", payload=event_data)
            
            self.logger.info(f"库存调整成功: id={inventory.id}")
            return self.create_success_result(inventory_response)
        except Exception as e:
            self.logger.error(f"调整库存失败: id={inventory_id}, 错误={str(e)}", exc_info=True)
            return self.create_error_result(
                error_code=ErrorCode.INTERNAL_ERROR,
                error_message=f"调整库存失败: {str(e)}"
            )
    
    async def reserve_inventory(
        self, 
        inventory_id: int, 
        params: InventoryReservationRequest
    ) -> Result[InventoryResponse]:
        """预留库存"""
        try:
            self.logger.info(f"预留库存: id={inventory_id}, quantity={params.quantity}")
            
            # 预留库存
            inventory = await self.inventory_repo.reserve_inventory(inventory_id, params.quantity)
            
            if not inventory:
                return self.create_error_result(
                    error_code=ErrorCode.OPERATION_FAILED,
                    error_message="库存预留失败，可能库存不足"
                )
            
            # 构建库存响应
            inventory_response = InventoryResponse.model_validate(inventory.to_dict())
            
            # 触发库存预留事件
            event_data = inventory_response.model_dump()
            event_data.update({
                "reserved_quantity": params.quantity,
                "order_id": params.order_id
            })
            dispatch("products:inventory:reserved", payload=event_data)
            
            self.logger.info(f"库存预留成功: id={inventory.id}")
            return self.create_success_result(inventory_response)
        except Exception as e:
            self.logger.error(f"预留库存失败: id={inventory_id}, 错误={str(e)}", exc_info=True)
            return self.create_error_result(
                error_code=ErrorCode.INTERNAL_ERROR,
                error_message=f"预留库存失败: {str(e)}"
            )
