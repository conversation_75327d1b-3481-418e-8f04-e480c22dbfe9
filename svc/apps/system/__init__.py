"""
系统模块
包含系统监控、审计日志和健康检查功能
"""

# 导出依赖函数
from svc.apps.system.dependencies import (get_audit_log_repository,
                                          get_audit_log_service,
                                          get_monitor_service,
                                          get_system_service)
# 导出事件
from svc.apps.system.events import audit_log_handlers, system_handlers
# 导出ORM模型
from svc.apps.system.models import AuditLog
# 导出仓库类
from svc.apps.system.repositories import AuditLogRepository
# 导出Schema模型
from svc.apps.system.schemas import AuditLogListResponse, AuditLogResponse
# 导出服务类
from svc.apps.system.services import (AuditLogService, SystemMonitorService,
                                      SystemService)
# 导出错误处理
from svc.core.exceptions import ErrorCode, handle_route_errors

__all__ = [
    # 模型
    "AuditLog",

    # Schema
    "AuditLogResponse",
    "AuditLogListResponse",

    # 服务类
    "SystemService",
    "AuditLogService",
    "SystemMonitorService",

    # 仓库类
    "AuditLogRepository",

    # 依赖函数
    "get_system_service",
    "get_audit_log_service",
    "get_monitor_service",
    "get_audit_log_repository",

    # 错误处理
    "ErrorCode",
    "handle_route_errors",

    # 事件
    "audit_log_handlers",
    "system_handlers"
]
