"""
数据库会话管理模块。
提供数据库连接、会话管理和依赖注入功能。
"""

# 标准库导入
import logging
import contextlib
import asyncio
from datetime import datetime, timedelta
from typing import AsyncGenerator, Optional, List, Set, Dict, Any, Tuple

# 第三方库导入
from sqlalchemy.ext.asyncio import AsyncSession, create_async_engine, AsyncEngine
from sqlalchemy.orm import sessionmaker, declarative_base
from sqlalchemy.pool import AsyncAdaptedQueuePool
from sqlalchemy.future import select

# 项目内部导入
from svc.core.config.settings import get_settings

# 日志配置
logger = logging.getLogger(__name__)

# 创建模型基类
Base = declarative_base()

# 全局状态
_engine: Optional[AsyncEngine] = None
_session_factory: Optional[sessionmaker] = None
_initialized = False
_active_sessions = 0  # 活跃会话计数（用于监控）
_connection_stats: Dict[str, int] = {
    "created": 0,
    "closed": 0,
    "errors": 0
}

# 会话跟踪信息
_session_tracking: Dict[int, Dict[str, Any]] = {}
_last_leak_check_time: Optional[datetime] = None

# 连接池恢复设置
_pool_recovery_in_progress = False

async def init_engine(testing: bool = False) -> AsyncEngine:
    """
    初始化数据库引擎。
    
    Args:
        testing: 是否为测试环境
        
    Returns:
        AsyncEngine: 异步数据库引擎实例
    """
    global _engine, _session_factory, _initialized
    
    if _initialized:
        return _engine
    
    # 加载配置
    settings = get_settings()
    
    # 获取数据库URI - 修复了测试环境URI的逻辑
    db_uri = settings.db_test_uri if testing else settings.db_uri
    # 创建数据库引擎
    _engine = create_async_engine(
        db_uri,
        echo=settings.db_echo,
        future=True,
        pool_pre_ping=True,
        poolclass=AsyncAdaptedQueuePool,
        pool_size=settings.db_pool_size,
        max_overflow=settings.db_max_overflow,
        pool_timeout=settings.db_pool_timeout,
        pool_recycle=settings.db_pool_recycle
    )
    
    # 创建会话工厂
    _session_factory = sessionmaker(
        _engine,
        class_=AsyncSession,
        expire_on_commit=False,
        autocommit=False,
        autoflush=False
    )
    
    _initialized = True
    logger.info(f"数据库引擎已初始化，连接池大小: {settings.db_pool_size}, 最大溢出: {settings.db_max_overflow}")
    return _engine

async def get_db() -> AsyncGenerator[AsyncSession, None]:
    """
    获取数据库会话的依赖函数
    
    跟踪会话创建时间，支持会话超时检测
    
    Yields:
        AsyncSession: 数据库会话
    """
    global _session_factory, _active_sessions, _connection_stats, _session_tracking
    
    # 确保引擎已初始化
    if not _initialized or _session_factory is None:
        await init_engine()
    
    # 创建会话
    session = _session_factory()
    session_id = id(session)
    creation_time = datetime.now()
    
    # 记录会话跟踪信息
    _session_tracking[session_id] = {
        "created_at": creation_time,
        "task_id": id(asyncio.current_task()) if asyncio.current_task() else None,
        "closed": False
    }
    
    _active_sessions += 1
    _connection_stats["created"] += 1
    logger.debug(f"会话已创建. 会话ID: {session_id}, 当前活跃: {_active_sessions}")
    
    try:
        yield session
        await session.commit()
    except Exception as e:
        await session.rollback()
        logger.error(f"会话操作失败: 会话ID: {session_id}, 错误: {str(e)}", exc_info=True)
        _connection_stats["errors"] += 1
        if session_id in _session_tracking:
            _session_tracking[session_id]["error"] = str(e)
        raise
    finally:
        # 检查会话状态，如果事务仍然活跃（未提交也未回滚），则回滚
        # if session.is_active:
        #     try:
        #         await session.rollback()
        #         logger.warning(f"会话在 finally 块中回滚. 会话ID: {session_id}. 可能存在未处理的异常或提前返回。")
        #     except Exception as rollback_exc:
        #         logger.error(f"在 finally 块中回滚会话失败: 会话ID: {session_id}, 错误: {rollback_exc}", exc_info=True)

        await session.close()
        _active_sessions -= 1
        _connection_stats["closed"] += 1
        
        # 更新会话跟踪信息
        if session_id in _session_tracking:
            duration = (datetime.now() - _session_tracking[session_id]["created_at"]).total_seconds()
            _session_tracking[session_id]["duration"] = duration
            _session_tracking[session_id]["closed"] = True
            
            # 如果会话使用时间超过阈值，记录警告
            threshold = get_settings().db_pool_timeout * 0.8
            if duration > threshold:
                logger.warning(f"检测到长时间会话: 会话ID: {session_id}, 持续时间: {duration:.2f}秒")
            
            # 清理已关闭的会话跟踪信息（延迟清理，保留一定历史记录用于调试）
            if len(_session_tracking) > 1000:  # 保留最近1000条记录
                _cleanup_session_tracking()
                
        logger.debug(f"会话已关闭. 会话ID: {session_id}, 当前活跃: {_active_sessions}")
    
    # 检查是否需要进行连接泄漏检测
    await _maybe_check_connection_leaks()

@contextlib.asynccontextmanager
async def get_session():
    """
    会话上下文管理器，用于手动管理会话的场景
    
    Example:
        ```
        async with get_session() as session:
            # 使用session
            await session.execute(...)
        # 此处session已自动关闭
        ```
    """
    global _session_factory, _active_sessions, _connection_stats, _session_tracking
    
    # 确保引擎已初始化
    if not _initialized or _session_factory is None:
        await init_engine()
    
    # 创建会话
    session = _session_factory()
    session_id = id(session)
    creation_time = datetime.now()
    
    # 记录会话跟踪信息
    _session_tracking[session_id] = {
        "created_at": creation_time,
        "context_manager": True,
        "task_id": id(asyncio.current_task()) if asyncio.current_task() else None,
        "closed": False
    }
    
    _active_sessions += 1
    _connection_stats["created"] += 1
    logger.debug(f"手动会话已创建. 会话ID: {session_id}, 当前活跃: {_active_sessions}")
    
    try:
        yield session
        await session.commit()
    except Exception as e:
        await session.rollback()
        logger.error(f"手动会话操作失败: 会话ID: {session_id}, 错误: {str(e)}", exc_info=True)
        _connection_stats["errors"] += 1
        if session_id in _session_tracking:
            _session_tracking[session_id]["error"] = str(e)
        raise
    finally:
        # 检查会话状态，如果事务仍然活跃（未提交也未回滚），则回滚
        if session.is_active:
            try:
                await session.rollback()
                logger.warning(f"会话在 finally 块中回滚. 会话ID: {session_id}. 可能存在未处理的异常或提前返回。")
            except Exception as rollback_exc:
                logger.error(f"在 finally 块中回滚会话失败: 会话ID: {session_id}, 错误: {rollback_exc}", exc_info=True)

        await session.close()
        _active_sessions -= 1
        _connection_stats["closed"] += 1
        
        # 更新会话跟踪信息
        if session_id in _session_tracking:
            duration = (datetime.now() - _session_tracking[session_id]["created_at"]).total_seconds()
            _session_tracking[session_id]["duration"] = duration
            _session_tracking[session_id]["closed"] = True
            
            # 如果会话使用时间超过阈值，记录警告
            threshold = get_settings().db_pool_timeout * 0.8
            if duration > threshold:
                logger.warning(f"检测到长时间手动会话: 会话ID: {session_id}, 持续时间: {duration:.2f}秒")
                
        logger.debug(f"手动会话已关闭. 会话ID: {session_id}, 当前活跃: {_active_sessions}")
    
    # 检查是否需要进行连接泄漏检测
    await _maybe_check_connection_leaks()

def _cleanup_session_tracking() -> None:
    """清理已关闭的会话跟踪信息"""
    global _session_tracking
    
    # 保留未关闭的会话和最近关闭的会话
    now = datetime.now()
    cutoff_time = now - timedelta(minutes=30)  # 保留30分钟内的记录
    
    to_remove = []
    for session_id, info in _session_tracking.items():
        if info.get("closed", False) and info.get("created_at", now) < cutoff_time:
            to_remove.append(session_id)
    
    # 删除旧记录
    for session_id in to_remove:
        del _session_tracking[session_id]
    
    logger.debug(f"已清理 {len(to_remove)} 条过期会话跟踪记录，当前跟踪: {len(_session_tracking)}")

async def _maybe_check_connection_leaks() -> None:
    """检查是否需要运行连接泄漏检测"""
    global _last_leak_check_time, _pool_recovery_in_progress
    
    # 避免频繁检查，只在必要时运行
    now = datetime.now()
    settings = get_settings()
    
    # 初始化最后检查时间
    if _last_leak_check_time is None:
        _last_leak_check_time = now
        return
    
    # 如果活跃会话数量超过阈值或距离上次检查已超过10分钟，执行检测
    check_interval = timedelta(minutes=10)
    session_threshold = settings.db_pool_size * 0.8
    
    if (_active_sessions > session_threshold or 
        now - _last_leak_check_time > check_interval) and not _pool_recovery_in_progress:
        _last_leak_check_time = now
        # 在后台任务中执行检测，避免阻塞
        asyncio.create_task(check_connection_leaks())

async def check_connection_leaks() -> Tuple[int, int]:
    """
    检测连接泄漏并尝试恢复
    
    Returns:
        Tuple[int, int]: (检测到的泄漏数, 恢复的连接数)
    """
    global _session_tracking, _active_sessions, _pool_recovery_in_progress
    
    if _pool_recovery_in_progress:
        logger.info("池恢复操作正在进行中，跳过泄漏检测")
        return (0, 0)
    
    logger.info(f"开始连接泄漏检测，当前活跃会话: {_active_sessions}")
    _pool_recovery_in_progress = True
    
    try:
        settings = get_settings()
        now = datetime.now()
        leaks_detected = 0
        recovered = 0
        
        # 查找长时间未关闭的会话
        session_timeout = timedelta(seconds=settings.db_pool_timeout * 2)  # 使用2倍超时时间作为泄漏判断
        
        for session_id, info in list(_session_tracking.items()):
            if not info.get("closed", False):
                session_age = now - info.get("created_at", now)
                if session_age > session_timeout:
                    leaks_detected += 1
                    logger.warning(
                        f"检测到可能的会话泄漏: 会话ID: {session_id}, "
                        f"已打开: {session_age.total_seconds():.1f}秒, "
                        f"创建于: {info.get('created_at').isoformat()}"
                    )
        
        # 如果泄漏数量超过临界值，执行连接池恢复
        critical_threshold = settings.db_pool_size * 0.5
        if leaks_detected > critical_threshold:
            logger.critical(
                f"检测到严重连接泄漏，泄漏数: {leaks_detected}, "
                f"活跃会话: {_active_sessions}, "
                f"尝试恢复连接池"
            )
            recovered = await recover_connection_pool()
        elif leaks_detected > 0:
            logger.warning(
                f"检测到 {leaks_detected} 个可能的连接泄漏，"
                f"但未达到恢复阈值 ({critical_threshold})"
            )
            
        return (leaks_detected, recovered)
    finally:
        _pool_recovery_in_progress = False

async def recover_connection_pool() -> int:
    """
    尝试恢复连接池状态
    
    Returns:
        int: 恢复的连接数
    """
    global _engine, _active_sessions, _session_tracking
    
    settings = get_settings()
    recovered = 0
    
    try:
        # 记录当前状态
        old_stats = get_connection_stats()
        logger.warning(f"开始恢复连接池，当前状态: {old_stats}")
        
        # 重置会话计数器（可能不准确）
        old_active = _active_sessions
        actual_active = await _get_actual_connection_count()
        
        if actual_active < old_active:
            recovered = old_active - actual_active
            _active_sessions = actual_active
            logger.info(f"重置会话计数: {old_active} -> {actual_active}")
        
        # 在极端情况下重置引擎
        allow_reset = getattr(settings, 'db_allow_pool_reset', False)
        critical_threshold = getattr(settings, 'db_critical_session_threshold', settings.db_pool_size * 2)
        
        if allow_reset and _active_sessions > critical_threshold:
            logger.critical(f"连接池状态危急，执行强制重置，当前活跃会话: {_active_sessions}")
            await close_db()
            await init_engine()
            recovered = old_active
            _active_sessions = 0
            _session_tracking.clear()
        
        # 记录恢复操作结果
        new_stats = get_connection_stats()
        logger.info(f"连接池恢复完成. 恢复前: {old_stats}, 恢复后: {new_stats}, 恢复连接: {recovered}")
        
        return recovered
    except Exception as e:
        logger.error(f"恢复连接池失败: {str(e)}", exc_info=True)
        return 0

async def _get_actual_connection_count() -> int:
    """获取实际连接数量"""
    global _engine
    
    if not _engine:
        return 0
    
    try:
        return _engine.pool.checkedout()
    except Exception:
        return _active_sessions  # 如果无法获取，返回当前计数

async def init_base_data(db: AsyncSession) -> None:
    """初始化基础数据（权限和角色）"""
    try:
        # 导入模型和仓库
        from svc.apps.auth.models import Role, Permission
        from svc.apps.auth.repositories import RoleRepository
        
        # 创建角色仓库实例
        role_repo = RoleRepository()
        
        # 收集所有权限
        all_permissions: Set[str] = set()
        default_roles = role_repo.get_default_roles()
        
        # 提取所有角色定义中的权限
        for role_config in default_roles:
            for permission in role_config.get("permissions", []):
                all_permissions.add(permission)
        
        # 为每个权限创建记录
        permission_objects = {}
        for permission_name in all_permissions:
            # 检查权限是否已存在
            result = await db.execute(
                select(Permission).where(Permission.name == permission_name)
            )
            permission = result.scalars().first()
            
            if not permission:
                # 创建新权限
                description = f"权限: {permission_name}"
                permission = Permission(
                    name=permission_name,
                    description=description
                )
                db.add(permission)
                await db.flush()  # 刷新会话以获取权限ID
            
            permission_objects[permission_name] = permission
        
        # 创建默认角色
        for role_config in default_roles:
            role_name = role_config["name"]
            
            # 检查角色是否已存在
            result = await db.execute(
                select(Role).where(Role.name == role_name)
            )
            role = result.scalars().first()
            
            if not role:
                # 创建新角色
                role = Role(
                    name=role_name,
                    description=role_config.get("description", f"角色: {role_name}")
                )
                db.add(role)
                await db.flush()  # 刷新会话以获取角色ID
            
            # 分配权限
            for permission_name in role_config.get("permissions", []):
                if permission_name in permission_objects:
                    # 使用仓库方法添加权限
                    await role_repo.add_permission(db, role, permission_objects[permission_name])
                else:
                    logger.warning(f"权限 '{permission_name}' 未定义，无法分配给 '{role_name}'")
        
        await db.commit()
        logger.info("基础数据初始化完成: 权限和角色已创建")
    except Exception as e:
        await db.rollback()
        logger.error(f"初始化基础数据失败: {str(e)}")
        raise

async def init_admin_user(db: AsyncSession) -> None:
    """初始化管理员用户"""
    try:
        # 导入模型和仓库
        from svc.apps.auth.models import User, Role
        from svc.apps.auth.repositories import UserRepository, RoleRepository
        from svc.core.config.settings import get_settings
        
        # 获取配置
        settings = get_settings()
        
        # 创建仓库实例
        user_repo = UserRepository()
        role_repo = RoleRepository()
        
        # 检查管理员用户是否已存在
        admin_username = settings.admin_username or "admin"
        admin_user = await user_repo.get_by_username(db, admin_username)
        
        if not admin_user:
            # 创建管理员用户
            admin_email = f"{admin_username}"
            admin_password = settings.admin_password or "admin123"
            
            admin_user = User(
                username=admin_username,
                email=admin_email,
                is_active=True,
                is_superuser=True,
                fullname="系统管理员"  # 添加fullname字段以满足数据库非空约束
            )
            
            # 设置密码
            admin_user.update_password(admin_password)
            
            # 保存用户
            db.add(admin_user)
            await db.flush()
            
            # 获取管理员角色并分配
            admin_role = await role_repo.get_by_name(db, "admin")
            if admin_role:
                await user_repo.add_role(db, admin_user, admin_role)
            
            await db.commit()
            logger.info(f"管理员用户已创建: {admin_username}")
        else:
            logger.info(f"管理员用户已存在: {admin_username}")
    except Exception as e:
        await db.rollback()
        logger.error(f"初始化管理员用户失败: {str(e)}")
        raise

async def init_db(testing: bool = False) -> None:
    """初始化数据库，创建所有表"""
    # 初始化数据库引擎
    engine = await init_engine(testing=testing)
    
    # 创建数据库表
    async with engine.begin() as conn:
        # 在生产环境中，应该使用迁移工具而不是直接创建表
        # 这里仅用于开发和测试环境
        await conn.run_sync(Base.metadata.create_all)
    
    logger.info("数据库表已创建")
    
    # 使用上下文管理器初始化数据，确保会话正确关闭
    # async with get_session() as session:
    #     await init_base_data(session)
    #     await init_admin_user(session)
        
    logger.info("数据库初始化完成")

async def close_db() -> None:
    """关闭数据库连接"""
    global _engine, _session_factory, _initialized, _active_sessions, _session_tracking
    
    if _engine:
        logger.info("正在关闭数据库连接...")
        
        # 检查是否有未关闭的会话，记录警告
        if _active_sessions > 0:
            logger.warning(f"关闭数据库时仍有 {_active_sessions} 个活跃会话！")
            
            # 记录未关闭会话的详细信息
            unclosed_sessions = []
            for session_id, info in _session_tracking.items():
                if not info.get("closed", False):
                    age = (datetime.now() - info.get("created_at", datetime.now())).total_seconds()
                    unclosed_sessions.append({
                        "id": session_id,
                        "age_seconds": age,
                        "created_at": info.get("created_at").isoformat()
                    })
            
            if unclosed_sessions:
                logger.warning(f"未关闭会话详情: {unclosed_sessions}")
            
        # 关闭连接池
        await _engine.dispose()
        _engine = None
        _session_factory = None
        _initialized = False
        _active_sessions = 0
        _session_tracking.clear()
        logger.info("数据库连接已关闭")

def get_connection_stats() -> Dict[str, Any]:
    """
    获取数据库连接统计信息
    
    Returns:
        Dict[str, Any]: 连接统计信息
    """
    global _engine, _active_sessions, _connection_stats, _session_tracking
    
    stats = _connection_stats.copy()
    stats["active"] = _active_sessions
    
    # 计算未关闭会话的平均年龄
    now = datetime.now()
    unclosed_sessions = [
        info for info in _session_tracking.values()
        if not info.get("closed", False)
    ]
    
    if unclosed_sessions:
        ages = [(now - info.get("created_at", now)).total_seconds() for info in unclosed_sessions]
        stats["unclosed_count"] = len(unclosed_sessions)
        stats["avg_unclosed_age"] = sum(ages) / len(ages)
        stats["max_unclosed_age"] = max(ages) if ages else 0
    else:
        stats["unclosed_count"] = 0
        stats["avg_unclosed_age"] = 0
        stats["max_unclosed_age"] = 0
    
    # 如果引擎已初始化，添加连接池状态
    if _engine:
        pool = _engine.pool
        stats["pool_size"] = pool.size()
        stats["pool_overflow"] = pool.overflow()
        stats["pool_checkedin"] = pool.checkedin()
        stats["pool_checkedout"] = pool.checkedout()
        stats["pool_checkedout_ratio"] = pool.checkedout() / pool.size() if pool.size() > 0 else 0
    
    return stats

def get_session_tracking_stats() -> Dict[str, Any]:
    """
    获取会话跟踪统计信息
    
    Returns:
        Dict[str, Any]: 会话跟踪统计信息
    """
    global _session_tracking
    
    now = datetime.now()
    closed_count = 0
    unclosed_count = 0
    error_count = 0
    age_buckets = {
        "0-10s": 0,
        "10-30s": 0,
        "30-60s": 0,
        "1-5min": 0,
        "5-15min": 0,
        "15min+": 0
    }
    
    # 统计会话状态
    for info in _session_tracking.values():
        if info.get("closed", False):
            closed_count += 1
        else:
            unclosed_count += 1
            age = (now - info.get("created_at", now)).total_seconds()
            
            # 分类到时间段
            if age < 10:
                age_buckets["0-10s"] += 1
            elif age < 30:
                age_buckets["10-30s"] += 1
            elif age < 60:
                age_buckets["30-60s"] += 1
            elif age < 300:
                age_buckets["1-5min"] += 1
            elif age < 900:
                age_buckets["5-15min"] += 1
            else:
                age_buckets["15min+"] += 1
        
        if "error" in info:
            error_count += 1
    
    return {
        "total": len(_session_tracking),
        "closed": closed_count,
        "unclosed": unclosed_count,
        "with_errors": error_count,
        "age_distribution": age_buckets
    }

async def ensure_connection_health() -> bool:
    """
    检查数据库连接健康状态
    
    Returns:
        bool: 连接是否健康
    """
    global _engine
    
    if not _engine:
        logger.error("数据库未初始化")
        return False
    
    try:
        # 从连接池获取连接并执行简单查询
        async with _engine.connect() as conn:
            await conn.execute(select(1))
        return True
    except Exception as e:
        logger.error(f"数据库连接检查失败: {str(e)}")
        return False

async def setup_db_pool_monitor(app=None) -> None:
    """
    设置数据库连接池监控
    
    Args:
        app: FastAPI应用实例，可选
    """
    # 创建后台任务定期检查连接池状态
    async def monitor_db_pool():
        try:
            settings = get_settings()
            check_interval = getattr(settings, 'db_monitor_interval', 300)  # 默认每5分钟检查
            while True:
                try:
                    # 检查连接池状态
                    stats = get_connection_stats()
                    
                    # 计算使用率
                    if stats.get("pool_size", 0) > 0:
                        usage_ratio = stats.get("pool_checkedout", 0) / stats.get("pool_size", 1)
                        
                        # 记录当前状态
                        logger.info(f"数据库连接池状态: 使用率={usage_ratio:.2f}, 活跃会话={stats['active']}")
                        
                        # 如果使用率超过75%，发出警告
                        if usage_ratio > 0.75:
                            logger.warning(f"数据库连接池使用率较高: {usage_ratio:.2f}")
                            
                            # 检查连接泄漏
                            await check_connection_leaks()
                    
                    # 运行会话清理
                    if len(_session_tracking) > 500:
                        _cleanup_session_tracking()
                        
                except Exception as e:
                    logger.error(f"连接池监控任务错误: {str(e)}")
                
                # 等待一段时间再次检查
                await asyncio.sleep(check_interval)
        except asyncio.CancelledError:
            logger.info("连接池监控任务已取消")
    
    # 启动监控任务
    task = asyncio.create_task(monitor_db_pool())
    
    # 如果提供了应用实例，在应用关闭时取消任务
    if app:
        @app.on_event("shutdown")
        async def shutdown_db_monitor():
            task.cancel()
            try:
                await task
            except asyncio.CancelledError:
                pass 