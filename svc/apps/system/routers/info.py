"""
系统信息路由模块。
提供系统信息查询的API接口。
"""

from typing import Any, Dict

from fastapi import APIRouter, Depends

from svc.apps.system.dependencies import get_system_service
from svc.apps.system.services.system import SystemService
from svc.core.services.result import Result

# 创建路由器
router = APIRouter(prefix="/info", tags=["系统信息"])

# === 路由处理函数 ===

@router.get("", response_model=Result[Dict[str, Any]], summary="获取系统信息")
async def get_system_info(
    system_service: SystemService = Depends(get_system_service)
):
    """
    获取系统基本信息
    
    返回系统的基本信息，包括名称、版本、状态和资源使用情况。
    
    - **name**: 系统名称
    - **version**: 系统版本
    - **status**: 系统状态
    - **uptime**: 系统运行时间
    - **cpu_usage**: CPU使用率
    - **memory_usage**: 内存使用量
    """
    return await system_service.get_system_info()

@router.get("/version", response_model=Result[Dict[str, str]], summary="获取版本信息")
async def get_version_info(
    system_service: SystemService = Depends(get_system_service)
):
    """
    获取系统版本信息
    
    返回系统的版本信息。
    
    - **version**: 系统版本
    - **build_time**: 构建时间
    - **git_commit**: Git提交哈希值
    """
    # 获取完整系统信息
    result = await system_service.get_system_info()
    if not result.is_success:
        return result
    
    # 过滤只返回版本相关信息
    version_info = {
        "version": result.data.get("version", "unknown"),
        "build_time": "2023-09-01T12:00:00Z",  # 示例值，实际应从系统获取
        "git_commit": "abc123def456"  # 示例值，实际应从系统获取
    }
    
    return Result(
        is_success=True,
        result_code=200,
        result_msg="获取成功",
        data=version_info
    )

@router.get("/status", response_model=Result[Dict[str, Any]], summary="获取系统状态")
async def get_system_status(
    system_service: SystemService = Depends(get_system_service)
):
    """
    获取系统状态信息
    
    返回系统的当前状态信息，包括运行状态和资源使用情况。
    
    - **status**: 系统状态
    - **uptime**: 系统运行时间
    - **cpu_usage**: CPU使用率
    - **memory_usage**: 内存使用量
    """
    # 获取完整系统信息
    result = await system_service.get_system_info()
    if not result.is_success:
        return result
    
    # 过滤只返回状态相关信息
    status_info = {
        "status": result.data.get("status", "unknown"),
        "uptime": result.data.get("uptime", "unknown"),
        "cpu_usage": result.data.get("cpu_usage", "unknown"),
        "memory_usage": result.data.get("memory_usage", "unknown")
    }
    
    return Result(
        is_success=True,
        result_code=200,
        result_msg="获取成功",
        data=status_info
    ) 