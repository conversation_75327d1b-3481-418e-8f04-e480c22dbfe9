"""
系统服务实现。
提供系统级功能的业务逻辑实现，协调审计日志和系统监控功能。
"""

import logging
from typing import Any, Dict, Optional

from redis.asyncio import Redis
from sqlalchemy.ext.asyncio import AsyncSession

from svc.apps.system.services.monitor import SystemMonitorService
from svc.core.services import BaseService
from svc.core.services.result import Result


class SystemService(BaseService[None, Result[Dict[str, Any]]]):
    """
    系统服务类，提供系统监控功能

    主要功能：
    - 提供系统信息查询接口
    - 系统健康状态检查
    - 系统性能指标监控
    """
    
    resource_type = "system"
    
    def __init__(
        self,
        db: AsyncSession,
        redis: Optional[Redis] = None,
        monitor_service: Optional[SystemMonitorService] = None
    ):
        """初始化系统服务

        Args:
            db: 数据库会话
            redis: Redis客户端（可选）
            monitor_service: 系统监控服务实例，不提供则创建新实例
        """
        super().__init__(redis)
        self.db = db
        self.logger = logging.getLogger(__name__)
        self.monitor_service = monitor_service or SystemMonitorService(db, redis)
    
    # 系统监控相关方法
    
    async def get_system_info(self) -> Result[Dict[str, Any]]:
        """
        获取系统信息
        
        Returns:
            Result[Dict[str, Any]]: 系统信息
        """
        return await self.monitor_service.get_system_info()
    
    async def get_health_status(self) -> Result[Dict[str, Any]]:
        """
        获取系统健康状态
        
        Returns:
            Result[Dict[str, Any]]: 健康状态信息
        """
        return await self.monitor_service.get_health_status()
    
    async def get_performance_metrics(self) -> Result[Dict[str, Any]]:
        """
        获取性能指标
        
        Returns:
            Result[Dict[str, Any]]: 性能指标
        """
        return await self.monitor_service.get_performance_metrics()
    

    

    

