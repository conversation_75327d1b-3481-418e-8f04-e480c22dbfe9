"""
商品管理模块依赖注入

该模块提供商品管理相关的依赖注入函数，
用于在FastAPI路由中注入服务和仓库实例。
"""
from typing import Optional

from fastapi import Depends
from redis.asyncio import Redis
from sqlalchemy.ext.asyncio import AsyncSession

from svc.apps.products.repositories.category import CategoryRepository
from svc.apps.products.repositories.inventory import InventoryRepository
from svc.apps.products.repositories.product import ProductRepository
from svc.apps.products.repositories.product_variant import \
    ProductVariantRepository
from svc.apps.products.services.category import CategoryService
from svc.apps.products.services.inventory import InventoryService
from svc.apps.products.services.product import ProductService
from svc.apps.products.services.product_variant import ProductVariantService
from svc.core.cache.redis import get_redis
from svc.core.database import get_session_for_route
from svc.core.dependencies.auth import configure_auth_dependencies

# === 仓库依赖注入 ===

async def get_product_repository(
    db: AsyncSession = Depends(get_session_for_route())
) -> ProductRepository:
    """获取商品仓库实例"""
    return ProductRepository(db)

async def get_category_repository(
    db: AsyncSession = Depends(get_session_for_route())
) -> CategoryRepository:
    """获取分类仓库实例"""
    return CategoryRepository(db)

async def get_product_variant_repository(
    db: AsyncSession = Depends(get_session_for_route())
) -> ProductVariantRepository:
    """获取商品变体仓库实例"""
    return ProductVariantRepository(db)

async def get_inventory_repository(
    db: AsyncSession = Depends(get_session_for_route())
) -> InventoryRepository:
    """获取库存仓库实例"""
    return InventoryRepository(db)

# === 服务依赖注入 ===

async def get_product_service(
    redis: Optional[Redis] = Depends(get_redis),
    product_repo: ProductRepository = Depends(get_product_repository)
) -> ProductService:
    """获取商品服务实例
    
    Args:
        redis: Redis客户端实例，用于缓存
        product_repo: 商品仓库实例
        
    Returns:
        ProductService: 商品服务实例
    """
    return ProductService(redis=redis, product_repo=product_repo)

async def get_category_service(
    redis: Optional[Redis] = Depends(get_redis),
    category_repo: CategoryRepository = Depends(get_category_repository)
) -> CategoryService:
    """获取分类服务实例
    
    Args:
        redis: Redis客户端实例，用于缓存
        category_repo: 分类仓库实例
        
    Returns:
        CategoryService: 分类服务实例
    """
    return CategoryService(redis=redis, category_repo=category_repo)

async def get_product_variant_service(
    redis: Optional[Redis] = Depends(get_redis),
    variant_repo: ProductVariantRepository = Depends(get_product_variant_repository)
) -> ProductVariantService:
    """获取商品变体服务实例
    
    Args:
        redis: Redis客户端实例，用于缓存
        variant_repo: 商品变体仓库实例
        
    Returns:
        ProductVariantService: 商品变体服务实例
    """
    return ProductVariantService(redis=redis, variant_repo=variant_repo)

async def get_inventory_service(
    redis: Optional[Redis] = Depends(get_redis),
    inventory_repo: InventoryRepository = Depends(get_inventory_repository)
) -> InventoryService:
    """获取库存服务实例
    
    Args:
        redis: Redis客户端实例，用于缓存
        inventory_repo: 库存仓库实例
        
    Returns:
        InventoryService: 库存服务实例
    """
    return InventoryService(redis=redis, inventory_repo=inventory_repo)

# === 组合服务依赖注入 ===

class ProductManagementServices:
    """商品管理服务集合类
    
    该类将所有商品管理相关的服务组合在一起，
    方便在需要多个服务协作的场景中使用。
    """
    
    def __init__(
        self,
        product_service: ProductService,
        category_service: CategoryService,
        variant_service: ProductVariantService,
        inventory_service: InventoryService
    ):
        self.product = product_service
        self.category = category_service
        self.variant = variant_service
        self.inventory = inventory_service

async def get_product_management_services(
    product_service: ProductService = Depends(get_product_service),
    category_service: CategoryService = Depends(get_category_service),
    variant_service: ProductVariantService = Depends(get_product_variant_service),
    inventory_service: InventoryService = Depends(get_inventory_service)
) -> ProductManagementServices:
    """获取商品管理服务集合
    
    Args:
        product_service: 商品服务实例
        category_service: 分类服务实例
        variant_service: 商品变体服务实例
        inventory_service: 库存服务实例
        
    Returns:
        ProductManagementServices: 商品管理服务集合实例
    """
    return ProductManagementServices(
        product_service=product_service,
        category_service=category_service,
        variant_service=variant_service,
        inventory_service=inventory_service
    )

# === 特殊依赖注入 ===

async def get_product_with_variants_service(
    services: ProductManagementServices = Depends(get_product_management_services)
) -> tuple[ProductService, ProductVariantService]:
    """获取商品和变体服务组合
    
    用于需要同时操作商品和变体的场景。
    
    Args:
        services: 商品管理服务集合
        
    Returns:
        tuple: (商品服务, 变体服务)
    """
    return services.product, services.variant

async def get_product_with_inventory_service(
    services: ProductManagementServices = Depends(get_product_management_services)
) -> tuple[ProductService, InventoryService]:
    """获取商品和库存服务组合
    
    用于需要同时操作商品和库存的场景。
    
    Args:
        services: 商品管理服务集合
        
    Returns:
        tuple: (商品服务, 库存服务)
    """
    return services.product, services.inventory

# === 模块设置函数 ===

def setup_products_dependencies():
    """
    设置商品管理依赖项

    注册商品管理相关的资源类型并配置全局认证依赖项。
    应在应用启动时调用此函数。
    """
    try:
        # 导入需要注册到权限系统的模型
        from svc.apps.products.models import (Category, Inventory, Product,
                                              ProductVariant)

        # 配置全局认证依赖项
        configure_auth_dependencies(
            resources={
                "product": Product,
                "category": Category,
                "product_variant": ProductVariant,
                "inventory": Inventory
            }
        )
    except ImportError:
        # 如果模型未定义或导入失败，记录日志但不中断程序
        import logging
        logger = logging.getLogger(__name__)
        logger.warning("未能注册商品管理模块资源类型，请检查模型是否正确定义")

# 在模块导入时自动调用设置函数
try:
    setup_products_dependencies()
except Exception as e:
    import logging
    logger = logging.getLogger(__name__)
    logger.error(f"设置商品管理依赖项失败: {str(e)}")
