from typing import Generic, TypeVar, List, Optional
from pydantic import BaseModel, Field

DataType = TypeVar('DataType')

class BaseResponse(BaseModel):
    """基础响应模型，可以包含通用字段"""
    is_success: bool = Field(True, description="操作是否成功")
    error_code: Optional[str] = Field(None, description="错误代码")
    error_message: Optional[str] = Field(None, description="错误信息")

class DataResponse(BaseResponse, Generic[DataType]):
    """包含数据的响应模型"""
    data: Optional[DataType] = Field(None, description="响应数据")


class PaginatedResponse(BaseModel, Generic[DataType]):
    """
    通用分页响应模型
    """
    items: List[DataType] = Field(..., description="当前页的数据列表")
    total: int = Field(..., description="总记录数")
    page: int = Field(..., description="当前页码 (从1开始)")
    size: int = Field(..., description="每页记录数")
    pages: int = Field(..., description="总页数") 