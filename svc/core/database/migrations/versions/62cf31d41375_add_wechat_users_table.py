"""add_wechat_users_table

版本信息：
- Revision ID: 62cf31d41375
- 上一版本: 2ed26f852690
- 创建时间: 2025-03-29 16:36:34.107299

变更说明：
创建微信用户表(wechat_users)，用于存储微信小程序授权用户信息

影响的表：
- wechat_users

注意事项：
- 执行前请确保users表已存在
"""

# 标准库导入
from datetime import datetime
from typing import Dict, List, Optional, Union

# 第三方库导入
from alembic import op
import sqlalchemy as sa
from sqlalchemy.dialects import postgresql


# 版本标识
revision = '62cf31d41375'
down_revision = '2ed26f852690'
branch_labels = None
depends_on = None


def upgrade() -> None:
    """执行迁移升级。
    
    本次升级包含以下变更：
    1. 创建wechat_users表
    2. 添加外键关联到users表
    
    注意：
    - 执行前请确保users表已存在
    """
    # 创建微信用户表
    op.create_table(
        'wechat_users',
        sa.Column('id', sa.Integer(), autoincrement=True, nullable=False),
        sa.Column('openid', sa.String(50), unique=True, index=True, nullable=False),
        sa.Column('unionid', sa.String(50), unique=True, index=True, nullable=True),
        sa.Column('user_id', sa.Integer(), nullable=True),
        
        # 微信用户信息
        sa.Column('nickname', sa.String(100), nullable=True),
        sa.Column('avatar_url', sa.String(255), nullable=True),
        sa.Column('gender', sa.Integer(), nullable=True),
        sa.Column('country', sa.String(50), nullable=True),
        sa.Column('province', sa.String(50), nullable=True),
        sa.Column('city', sa.String(50), nullable=True),
        sa.Column('language', sa.String(20), nullable=True),
        
        # 会话信息
        sa.Column('session_key', sa.String(100), nullable=True),
        
        # 状态
        sa.Column('is_active', sa.Boolean(), default=True),
        
        # 时间戳
        sa.Column('created_at', sa.DateTime(timezone=True), nullable=False),
        sa.Column('updated_at', sa.DateTime(timezone=True), nullable=False),
        sa.Column('last_login', sa.DateTime(timezone=True), nullable=True),
        
        # 主键和外键
        sa.PrimaryKeyConstraint('id'),
        sa.ForeignKeyConstraint(['user_id'], ['users.id'], ondelete='SET NULL'),
    )
    
    # 创建索引
    op.create_index('ix_wechat_users_openid', 'wechat_users', ['openid'], unique=True)
    op.create_index('ix_wechat_users_unionid', 'wechat_users', ['unionid'], unique=True)
    op.create_index('ix_wechat_users_user_id', 'wechat_users', ['user_id'], unique=False)


def downgrade() -> None:
    """执行迁移回滚。
    
    本次回滚将：
    1. 删除wechat_users表的索引
    2. 删除wechat_users表
    
    注意：
    - 回滚顺序与升级顺序相反
    - 请确保数据已备份
    """
    # 删除索引
    op.drop_index('ix_wechat_users_user_id', 'wechat_users')
    op.drop_index('ix_wechat_users_unionid', 'wechat_users')
    op.drop_index('ix_wechat_users_openid', 'wechat_users')
    
    # 删除表
    op.drop_table('wechat_users') 