"""
商品管理模块业务逻辑层

该模块包含商品管理相关的服务类定义：
- ProductService: 商品业务逻辑
- CategoryService: 分类业务逻辑
- ProductVariantService: 商品变体业务逻辑
- InventoryService: 库存业务逻辑
"""

from .product import ProductService
from .category import CategoryService
from .product_variant import ProductVariantService
from .inventory import InventoryService

__all__ = [
    "ProductService",
    "CategoryService",
    "ProductVariantService", 
    "InventoryService"
]
