"""
示例路由模块，展示标准化会话获取和事务管理装饰器的使用。
"""
from typing import Dict, Any, List, Optional
from pydantic import BaseModel, Field

from fastapi import APIRouter, Depends, Path, Query
from sqlalchemy.ext.asyncio import AsyncSession

from svc.apps.system.schemas.config import ConfigItemCreate, ConfigItemUpdate, ConfigListResponse
from svc.core.services.result import Result
from svc.core.database import get_session_for_route, transactional_route
from svc.apps.system.services.config import ConfigService
from svc.apps.system.dependencies import get_config_service

# 创建路由器
router = APIRouter(tags=["配置"])


# === 路由处理函数 ===

# 方式1: 使用标准的依赖注入方式获取会话
@router.get("/configs/{config_id}", response_model=Result)
async def get_config(
    config_id: int = Path(..., description="配置项ID"),
    config_service: ConfigService = Depends(get_config_service)
):
    """
    获取配置项
    
    使用标准的依赖注入方式，会话通过服务依赖注入。
    """
    return await config_service.get_config(config_id)

# 方式2: 使用标准化的get_session_for_route获取会话
@router.get("/", response_model=Result)
async def get_configs(
    prefix: Optional[str] = Query(None, description="按前缀过滤"),
    skip: int = Query(0, ge=0, description="跳过记录数"),
    limit: int = Query(100, ge=1, le=1000, description="每页记录数"),
    config_service: ConfigService = Depends(get_config_service)
):
    """
    获取配置项列表
    
    使用标准化的get_session_for_route获取会话。
    """
    return await config_service.get_configs(prefix=prefix, skip=skip, limit=limit)

# 方式3: 使用transactional_route装饰器管理事务
@router.post("/configs", response_model=Result)
@transactional_route(auto_commit=True)
async def create_config(
    config_data: ConfigItemCreate,
    config_service: ConfigService = Depends(get_config_service)
):
    """
    创建配置项
    
    使用transactional_route装饰器管理事务，
    自动处理提交和回滚。
    """
    return await config_service.create_config(config_data=config_data)

# 方式4: 使用transactional_route装饰器并设置超时
@router.put("/configs/{config_id}", response_model=Result)
@transactional_route(timeout_seconds=10)
async def update_config(
    config_data: ConfigItemUpdate,
    config_id: int = Path(..., description="配置项ID"),
    config_service: ConfigService = Depends(get_config_service)
):
    """
    更新配置项
    
    使用transactional_route装饰器管理事务，
    并设置10秒超时，防止长时间运行的事务。
    """
    return await config_service.update_config(config_id=config_id, config_data=config_data)

# 方式5: 使用应用特定的数据库会话依赖
@router.delete("/configs/{config_id}", response_model=Result)
async def delete_config(
    config_id: int = Path(..., description="配置项ID"),
    config_service: ConfigService = Depends(get_config_service)
):
    """
    删除配置项
    
    使用应用特定的数据库会话依赖。
    """
    return await config_service.delete_config(config_id=config_id)

# 方式6: 批量操作使用事务和超时控制
@router.post("/configs/batch-update", response_model=Result)
@transactional_route(timeout_seconds=30)
async def batch_update_configs(
    prefix: str = Query(..., description="配置项键前缀"),
    new_value: str = Query(..., description="新的配置值"),
    config_service: ConfigService = Depends(get_config_service)
):
    """
    批量更新配置项
    
    使用事务和超时控制，防止长时间运行的事务。
    此路由展示了如何将服务方法与路由装饰器结合使用。
    """
    # 注意：服务方法也有自己的事务装饰器，这里会形成嵌套事务
    return await config_service.update_all_configs(prefix, new_value, config_service.db) 