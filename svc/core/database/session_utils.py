"""
会话工具模块。
提供会话获取和事务处理的标准化函数和装饰器。
解决会话获取方式多样问题，统一规范会话使用模式。
"""

import functools
import logging
from typing import Callable, TypeVar, Any, Optional, Union, cast, Awaitable

from fastapi import Depends
from sqlalchemy.ext.asyncio import AsyncSession

from svc.core.database.session import get_db, get_session
from svc.core.database.transactions import (
    with_transaction as _with_transaction,
    with_transaction_timeout as _with_transaction_timeout
)

# 类型变量定义
T = TypeVar('T')
F = TypeVar('F', bound=Callable[..., Any])

# 配置日志
logger = logging.getLogger(__name__)

# ===== 会话获取方式标准化 =====

def get_session_for_route() -> Callable[[], AsyncSession]:
    """
    获取用于API路由的数据库会话依赖函数
    
    推荐在API路由处理函数中使用此依赖:
    
    Example:
        ```python
        @router.get("/users/{user_id}")
        async def get_user(user_id: int, db: AsyncSession = Depends(get_session_for_route())):
            # 使用db会话
            user = await user_service.get_user(db, user_id)
            return user
        ```
    
    Returns:
        Callable: 依赖函数
    """
    return get_db


async def get_session_for_script() -> AsyncSession:
    """
    获取用于脚本和后台任务的数据库会话
    
    此函数返回一个已经创建好的会话对象，需要手动管理其生命周期。
    
    Example:
        ```python
        async def update_stats():
            session = await get_session_for_script()
            try:
                # 使用session
                await session.execute(query)
                await session.commit()
            except Exception as e:
                await session.rollback()
                raise
            finally:
                await session.close()
        ```
    
    Returns:
        AsyncSession: 数据库会话
    """
    # 创建会话 - 注意：这个会话需要由调用者手动关闭
    session = None
    async with get_session() as s:
        session = s
        # 防止会话被上下文管理器关闭
        await s.begin_nested()
    
    if session is None:
        raise RuntimeError("无法创建数据库会话")
    
    return session


# ===== 事务管理装饰器 =====

def with_transaction(auto_commit: bool = True):
    """
    事务装饰器，自动处理会话创建、提交和回滚
    
    这是一个统一规范的用法推荐，内部调用现有的with_transaction实现。
    
    Example:
        ```python
        # 用于服务层方法
        @with_transaction()
        async def create_user(self, db: AsyncSession, data: dict):
            user = User(**data)
            db.add(user)
            return user
        ```
    
    Args:
        auto_commit: 是否自动提交事务
        
    Returns:
        装饰器函数
    """
    return _with_transaction(auto_commit=auto_commit)


def with_transaction_timeout(timeout_seconds: Optional[int] = None):
    """
    事务超时装饰器，在超时时自动回滚事务
    
    这是一个统一规范的用法推荐，内部调用现有的with_transaction_timeout实现。
    
    Example:
        ```python
        # 用于可能长时间运行的操作
        @with_transaction_timeout(timeout_seconds=30)
        async def process_large_dataset(self, db: AsyncSession, data_ids: list):
            for id in data_ids:
                # 处理每条数据...
            return result
        ```
    
    Args:
        timeout_seconds: 超时秒数，如果为None则使用配置的默认值
        
    Returns:
        装饰器函数
    """
    return _with_transaction_timeout(timeout_seconds=timeout_seconds)


# ===== 用于路由层的事务装饰器 =====

def transactional_route(auto_commit: bool = True, timeout_seconds: Optional[int] = None):
    """
    用于API路由的事务管理装饰器
    
    将路由处理函数包装在事务中，支持超时控制。
    此装饰器需放在路由装饰器(@router.get等)之后使用。
    
    Example:
        ```python
        @router.post("/users/")
        @transactional_route(timeout_seconds=10)
        async def create_user(data: UserCreate, db: AsyncSession = Depends(get_session_for_route())):
            user = User(**data.dict())
            db.add(user)
            return user
        ```
    
    Args:
        auto_commit: 是否自动提交事务
        timeout_seconds: 超时秒数，如果为None则使用配置的默认值
        
    Returns:
        装饰器函数
    """
    def decorator(func: F) -> F:
        @functools.wraps(func)
        async def wrapper(*args: Any, **kwargs: Any) -> Any:
            # 应用事务超时装饰器
            if timeout_seconds is not None:
                func_with_timeout = _with_transaction_timeout(timeout_seconds)(func)
            else:
                func_with_timeout = func
                
            # 处理自动提交
            db = kwargs.get('db')
            try:
                result = await func_with_timeout(*args, **kwargs)
                if auto_commit and db is not None and hasattr(db, 'commit'):
                    await db.commit()
                return result
            except Exception as e:
                if db is not None and hasattr(db, 'rollback'):
                    await db.rollback()
                logger.error(f"路由事务操作失败: {func.__name__}, 错误: {str(e)}")
                raise
        
        return cast(F, wrapper)
    
    return decorator


# ===== 日志增强装饰器 =====

def log_db_operation(operation_name: str):
    """
    数据库操作日志记录装饰器
    
    为操作添加统一格式的日志记录，包括操作名称、参数信息等。
    
    Example:
        ```python
        @log_db_operation("创建用户")
        async def create_user(self, db: AsyncSession, data: dict):
            # 数据库操作...
        ```
    
    Args:
        operation_name: 操作名称
        
    Returns:
        装饰器函数
    """
    def decorator(func: F) -> F:
        @functools.wraps(func)
        async def wrapper(*args: Any, **kwargs: Any) -> Any:
            # 记录操作开始
            func_name = f"{func.__module__}.{func.__name__}"
            safe_kwargs = {k: v for k, v in kwargs.items() if k != 'db' and k != 'password'}
            logger.info(f"数据库操作开始: {operation_name} [{func_name}], 参数: {safe_kwargs}")
            
            start_time = __import__('time').time()
            try:
                result = await func(*args, **kwargs)
                execution_time = __import__('time').time() - start_time
                
                # 记录操作成功
                logger.info(
                    f"数据库操作成功: {operation_name} [{func_name}], "
                    f"耗时: {execution_time:.3f}秒"
                )
                return result
            except Exception as e:
                execution_time = __import__('time').time() - start_time
                
                # 记录操作失败
                logger.error(
                    f"数据库操作失败: {operation_name} [{func_name}], "
                    f"耗时: {execution_time:.3f}秒, 错误: {str(e)}"
                )
                raise
        
        return cast(F, wrapper)
    
    return decorator 