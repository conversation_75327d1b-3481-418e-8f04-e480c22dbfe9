"""
商品分类模型，定义商品分类的基本属性和规则
"""
import enum
from sqlalchemy import Column, BigInteger, String, DateTime, Boolean, Integer, Text, ForeignKey, Enum
from sqlalchemy.orm import relationship
from typing import List, Optional, Dict

from svc.core.database.session import Base
from svc.core.models.custom_types import DatabaseCompatibleJSON
from svc.core.utils.datetime_utils import get_utc_now_without_tzinfo

# 分类状态枚举
class CategoryStatus(str, enum.Enum):
    ACTIVE = "active"      # 启用
    INACTIVE = "inactive"  # 禁用
    DELETED = "deleted"    # 已删除

class Category(Base):
    """
    商品分类模型，定义商品分类的基本属性和规则
    """
    __tablename__ = "categories"
    
    id = Column(BigInteger, primary_key=True, autoincrement=True)
    name = Column(String(100), nullable=False, comment="分类名称")
    description = Column(Text, nullable=True, comment="分类描述")
    slug = Column(String(100), nullable=False, unique=True, index=True, comment="分类别名")
    
    # 层级结构
    parent_id = Column(BigInteger, ForeignKey("categories.id"), nullable=True, comment="父分类ID")
    level = Column(Integer, default=0, comment="分类层级")
    sort_order = Column(Integer, default=0, comment="排序顺序")
    
    # 分类状态
    status = Column(String(20), nullable=False, default=CategoryStatus.ACTIVE, comment="分类状态")
    is_featured = Column(Boolean, default=False, comment="是否为推荐分类")
    
    # 分类属性
    image_url = Column(String(500), nullable=True, comment="分类图片URL")
    icon = Column(String(100), nullable=True, comment="分类图标")
    attributes = Column(DatabaseCompatibleJSON, default=dict, comment="分类属性")
    meta_data = Column(DatabaseCompatibleJSON, default=dict, comment="元数据")
    
    # SEO信息
    seo_title = Column(String(200), nullable=True, comment="SEO标题")
    seo_description = Column(Text, nullable=True, comment="SEO描述")
    seo_keywords = Column(String(500), nullable=True, comment="SEO关键词")
    
    # 统计数据
    product_count = Column(Integer, default=0, comment="商品数量")
    
    # 创建和更新时间
    created_at = Column(DateTime, nullable=False, default=get_utc_now_without_tzinfo)
    updated_at = Column(DateTime, nullable=False, default=get_utc_now_without_tzinfo, 
                        onupdate=get_utc_now_without_tzinfo)
    
    # 关系
    parent = relationship("Category", remote_side=[id], back_populates="children", lazy="selectin")
    children = relationship("Category", back_populates="parent", cascade="all, delete-orphan", lazy="dynamic")
    products = relationship("Product", back_populates="category", cascade="all, delete-orphan", lazy="dynamic")
    
    def is_active(self) -> bool:
        """判断分类是否启用"""
        return self.status == CategoryStatus.ACTIVE
        
    def get_full_path(self) -> str:
        """获取分类的完整路径"""
        if self.parent:
            return f"{self.parent.get_full_path()} > {self.name}"
        return self.name
        
    def to_dict(self) -> Dict:
        """将分类对象转换为字典，用于JSON序列化和缓存
        
        Returns:
            dict: 分类的字典表示
        """
        return {
            "id": self.id,
            "name": self.name,
            "description": self.description,
            "slug": self.slug,
            "parent_id": self.parent_id,
            "level": self.level,
            "sort_order": self.sort_order,
            "status": self.status,
            "is_featured": self.is_featured,
            "image_url": self.image_url,
            "icon": self.icon,
            "attributes": self.attributes,
            "meta_data": self.meta_data,
            "seo_title": self.seo_title,
            "seo_description": self.seo_description,
            "seo_keywords": self.seo_keywords,
            "product_count": self.product_count,
            "created_at": self.created_at.isoformat() if self.created_at else None,
            "updated_at": self.updated_at.isoformat() if self.updated_at else None
        }
