"""
审计日志API路由。
提供查询系统审计日志的API端点。
"""

from datetime import datetime
from typing import Any, Dict, List, Optional

from fastapi import APIRouter, Depends, HTTPException, Path, Query

from svc.apps.auth.dependencies import get_current_superuser, get_current_user
from svc.apps.auth.models.user import User
from svc.apps.system.dependencies import get_audit_log_service
from svc.apps.system.services.audit_log import AuditLogService
from svc.core.services.result import Result
from svc.core.utils.datetime_utils import make_naive


def parse_datetime_string(date_str: Optional[str]) -> Optional[datetime]:
    """
    解析日期字符串为datetime对象

    Args:
        date_str: 日期字符串，支持格式：
                 - YYYY-MM-DD
                 - YYYY-MM-DDTHH:MM:SS
                 - YYYY-MM-DDTHH:MM:SS.fZ

    Returns:
        datetime对象或None（返回naive datetime以兼容数据库）
    """
    if not date_str:
        return None

    try:
        # 尝试解析ISO格式
        if 'T' in date_str:
            # 处理带时间的格式
            if date_str.endswith('Z'):
                date_str = date_str[:-1] + '+00:00'
            dt = datetime.fromisoformat(date_str)
        else:
            # 处理只有日期的格式
            dt = datetime.fromisoformat(date_str + 'T00:00:00')

        # 使用工具函数转换为naive datetime以兼容数据库字段（TIMESTAMP WITHOUT TIME ZONE）
        return make_naive(dt)
    except ValueError:
        raise HTTPException(
            status_code=422,
            detail=f"Invalid date format: {date_str}. Expected ISO format like YYYY-MM-DD or YYYY-MM-DDTHH:MM:SS"
        )

# 创建路由器
router = APIRouter(
    tags=["审计日志"]
)

@router.get(
    "",
    response_model=Result[Dict[str, Any]],
    summary="查询审计日志列表",
    description="获取系统审计日志列表，支持按各种条件过滤和分页"
)
async def search_audit_logs(
    action: Optional[str] = Query(None, description="操作类型"),
    resource_type: Optional[str] = Query(None, description="资源类型"),
    resource_id: Optional[str] = Query(None, description="资源ID"),
    user_id: Optional[int] = Query(None, description="用户ID"),
    username: Optional[str] = Query(None, description="用户名"),
    status: Optional[str] = Query(None, description="操作结果"),
    start_date: Optional[str] = Query(None, description="开始日期 (ISO格式: YYYY-MM-DD 或 YYYY-MM-DDTHH:MM:SS)"),
    end_date: Optional[str] = Query(None, description="结束日期 (ISO格式: YYYY-MM-DD 或 YYYY-MM-DDTHH:MM:SS)"),
    page: int = Query(1, ge=1, description="页码"),
    page_size: int = Query(20, ge=1, le=100, description="每页条数"),
    audit_log_service: AuditLogService = Depends(get_audit_log_service),
    current_user: User = Depends(get_current_superuser)  # 权限验证：需要超级管理员
) -> Result[Dict[str, Any]]:
    """
    查询审计日志列表

    Args:
        action: 操作类型
        resource_type: 资源类型
        resource_id: 资源ID
        user_id: 用户ID
        username: 用户名
        status: 操作结果
        start_date: 开始日期
        end_date: 结束日期
        page: 页码
        page_size: 每页条数
        current_user: 当前用户（需要超级管理员权限）

    Returns:
        Result[Dict[str, Any]]: 审计日志列表和分页信息
    """
    # 转换日期字符串为datetime对象
    parsed_start_date = parse_datetime_string(start_date)
    parsed_end_date = parse_datetime_string(end_date)

    return await audit_log_service.search_audit_logs(
        action=action,
        resource_type=resource_type,
        resource_id=resource_id,
        user_id=user_id,
        username=username,
        status=status,
        start_date=parsed_start_date,
        end_date=parsed_end_date,
        page=page,
        page_size=page_size
    )

@router.get(
    "/latest",
    response_model=Result[List[Dict[str, Any]]],
    summary="获取最新审计日志",
    description="获取系统中最新的几条审计日志记录"
)
async def get_latest_audit_logs(
    limit: int = Query(10, ge=1, le=100, description="返回数量限制"),
    audit_log_service: AuditLogService = Depends(get_audit_log_service),
    current_user: User = Depends(get_current_superuser)  # 权限验证：需要超级管理员
) -> Result[List[Dict[str, Any]]]:
    """
    获取最新审计日志

    Args:
        limit: 返回数量限制
        current_user: 当前用户（需要超级管理员权限）

    Returns:
        Result[List[Dict[str, Any]]]: 审计日志列表
    """
    return await audit_log_service.get_latest_audit_logs(limit)

@router.get(
    "/statistics",
    response_model=Result[Dict[str, Any]],
    summary="获取审计日志统计",
    description="获取审计日志的统计信息，包括操作类型分布、用户活跃度等"
)
async def get_audit_statistics(
    start_date: Optional[str] = Query(None, description="开始日期 (ISO格式: YYYY-MM-DD 或 YYYY-MM-DDTHH:MM:SS)"),
    end_date: Optional[str] = Query(None, description="结束日期 (ISO格式: YYYY-MM-DD 或 YYYY-MM-DDTHH:MM:SS)"),
    audit_log_service: AuditLogService = Depends(get_audit_log_service),
    current_user: User = Depends(get_current_superuser)  # 权限验证：需要超级管理员
) -> Result[Dict[str, Any]]:
    """
    获取审计日志统计信息

    Args:
        start_date: 开始日期，默认为最近30天
        end_date: 结束日期，默认为当前时间
        current_user: 当前用户（需要超级管理员权限）

    Returns:
        Result[Dict[str, Any]]: 统计信息
    """
    # 转换日期字符串为datetime对象
    parsed_start_date = parse_datetime_string(start_date)
    parsed_end_date = parse_datetime_string(end_date)

    return await audit_log_service.get_statistics(parsed_start_date, parsed_end_date)

@router.get(
    "/resources/{resource_type}/{resource_id}",
    response_model=Result[List[Dict[str, Any]]],
    summary="获取资源审计日志",
    description="获取特定资源的所有审计日志记录"
)
async def get_resource_audit_logs(
    resource_type: str = Path(..., description="资源类型"),
    resource_id: str = Path(..., description="资源ID"),
    audit_log_service: AuditLogService = Depends(get_audit_log_service),
    current_user: User = Depends(get_current_user)  # 权限验证：需要登录
) -> Result[List[Dict[str, Any]]]:
    """
    获取资源审计日志

    Args:
        resource_type: 资源类型
        resource_id: 资源ID
        current_user: 当前用户（需要登录）

    Returns:
        Result[List[Dict[str, Any]]]: 审计日志列表
    """
    return await audit_log_service.get_resource_logs(resource_type, resource_id)


@router.get(
    "/users/{user_id}",
    response_model=Result[List[Dict[str, Any]]],
    summary="获取用户审计日志",
    description="获取特定用户的所有审计日志记录"
)
async def get_user_audit_logs(
    user_id: int = Path(..., description="用户ID"),
    limit: int = Query(50, ge=1, le=200, description="返回数量限制"),
    audit_log_service: AuditLogService = Depends(get_audit_log_service),
    current_user: User = Depends(get_current_user)
) -> Result[List[Dict[str, Any]]]:
    """
    获取用户审计日志

    Args:
        user_id: 用户ID
        limit: 返回数量限制
        current_user: 当前用户

    Returns:
        Result[List[Dict[str, Any]]]: 审计日志列表
    """
    # 权限检查：只能查看自己的日志，除非是超级管理员
    if current_user.id != user_id and not current_user.is_superuser:
        raise HTTPException(
            status_code=403,
            detail="无权限查看其他用户的审计日志"
        )

    return await audit_log_service.get_user_logs(user_id, limit)

@router.get(
    "/{log_id}",
    response_model=Result[Dict[str, Any]],
    summary="获取审计日志详情",
    description="获取单个审计日志的详细信息"
)
async def get_audit_log(
    log_id: int = Path(..., description="审计日志ID"),
    audit_log_service: AuditLogService = Depends(get_audit_log_service),
    current_user: User = Depends(get_current_superuser)  # 权限验证：需要超级管理员
) -> Result[Dict[str, Any]]:
    """
    获取审计日志详情

    Args:
        log_id: 审计日志ID
        current_user: 当前用户（需要超级管理员权限）

    Returns:
        Result[Dict[str, Any]]: 审计日志详情
    """
    return await audit_log_service.get_by_id(log_id)