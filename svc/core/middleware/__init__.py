# core/middleware/__init__.py
from .base import BaseMiddleware
from .registry import setup_middlewares, MiddlewareRegistry
from .request_id import RequestIdMiddleware
from .auth import AuthMiddleware
from .rate_limit import RateLimitMiddleware
from .cors import CORSMiddleware
from .logging import RequestLoggingMiddleware
from .security import SecurityHeadersMiddleware
from .exception import ExceptionHandlerMiddleware

__all__ = [
    # 中间件基础类
    'BaseMiddleware',
    'MiddlewareRegistry',
    'setup_middlewares',
    'middleware_config',
    
    # 中间件类
    'RequestIdMiddleware',
    'AuthMiddleware',
    'RateLimitMiddleware',
    'CORSMiddleware',
    'RequestLoggingMiddleware',
    'SecurityHeadersMiddleware',
    'ExceptionHandlerMiddleware',
]

# 中间件配置
middleware_config = {
    # 请求ID中间件配置
    "requestid": {
        "enabled": True,
        "options": {
            "header_name": "X-Request-ID",
            "include_in_response": True
        }
    },
    
    # 认证中间件配置
    "auth": {
        "enabled": True,
        "options": {
            "exclude_paths": [
                # Existing common paths
                '/docs', 
                '/redoc', 
                '/openapi.json',
                # Auth endpoints
                '/api/v1/auth/login', 
                '/api/v1/auth/register',
                '/api/v1/wechat/login',
                # Billing public paths (Updated based on refactoring)
                '/api/v1/subscription_plans/list',
                '/api/v1/subscription_plans/active',
                '/api/v1/subscription_plans/details/{plan_id}',
                '/api/v1/payments/callback', # Payment gateway callback
                # Marketing public paths
                '/api/v1/campaign/active',
                '/api/v1/campaign/details/{campaign_id}',
                '/api/v1/invitation/validate/{code}',
            ],
            "use_oauth2": False
        }
    },
    
    # 速率限制中间件配置
    "ratelimit": {
        "enabled": True,
        "options": {
            "limit": 100,
            "window": 60
        }
    },
    
    # CORS中间件配置
    "cors": {
        "enabled": True,
        "options": {
            "allow_origins": ["*"],
            "allow_methods": ["*"],
            "allow_headers": ["*"]
        }
    },
    
    # 请求日志中间件配置
    "requestlogging": {
        "enabled": True,
        "options": {
            "exclude_paths": ["/health"]
        }
    },
    
    # 安全头中间件配置
    "securityheaders": {
        "enabled": False,
        "options": {}
    },
    
    # 异常处理中间件配置
    "exceptionhandler": {
        "enabled": True,
        "options": {}
    },
    
}

# 中间件优先级顺序
MIDDLEWARE_PRIORITY = [
    "requestid",    # 1. 请求ID (最高优先级)
    "cors",          # 2. CORS
    "securityheaders", # 3. 安全头
    "ratelimit",    # 4. 速率限制
    "requestlogging", # 5. 请求日志
    "auth",          # 6. 认证
    "exceptionhandler", # 7. 异常处理
]
