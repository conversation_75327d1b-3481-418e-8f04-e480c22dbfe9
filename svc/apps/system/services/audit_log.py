"""
审计日志服务实现。
专门负责操作日志的记录、查询、分析等功能。
"""

import logging
from datetime import datetime, timedelta, timezone
from typing import Any, Dict, List, Optional

from fastapi import Request
from redis.asyncio import Redis
from sqlalchemy.ext.asyncio import AsyncSession

from svc.apps.system.models.audit_log import AuditLog
from svc.apps.system.repositories.audit_log import AuditLogRepository
from svc.core.exceptions.error_codes import ErrorCode
from svc.core.services import BaseService
from svc.core.services.result import Result, ResultFactory


class AuditLogService(BaseService[AuditLog, Result[Dict[str, Any]]]):
    """
    审计日志服务类，专门处理操作日志相关功能

    主要功能：
    - 记录操作日志
    - 查询和搜索日志
    - 日志统计分析
    - 日志清理和归档
    """

    resource_type = "audit_log"
    
    def __init__(
        self, 
        db: AsyncSession, 
        redis: Optional[Redis] = None,
        audit_log_repo: Optional[AuditLogRepository] = None
    ):
        """初始化审计日志服务
        
        Args:
            db: 数据库会话
            redis: Redis客户端（可选）
            audit_log_repo: 审计日志仓库实例，不提供则创建新实例
        """
        super().__init__(db, redis)
        self.logger = logging.getLogger(__name__)
        self.audit_log_repo = audit_log_repo or AuditLogRepository()
    
    async def get_audit_log(self, log_id: int) -> Result[Dict[str, Any]]:
        """
        获取审计日志详情
        
        Args:
            log_id: 审计日志ID
            
        Returns:
            Result[Dict[str, Any]]: 审计日志详情
        """
        try:
            log = await self.audit_log_repo.get_by_id(self.db, log_id)
            if not log:
                return ResultFactory.error(
                    ErrorCode.NOT_FOUND, 
                    f"审计日志(ID={log_id})不存在"
                )
                
            return ResultFactory.success(log.to_dict())
        except Exception as e:
            self.logger.error(f"获取审计日志失败: {str(e)}", exc_info=True)
            return ResultFactory.error(ErrorCode.INTERNAL_ERROR, f"获取审计日志失败: {str(e)}")
    
    async def search_audit_logs(
        self,
        action: Optional[str] = None,
        resource_type: Optional[str] = None,
        resource_id: Optional[str] = None,
        user_id: Optional[int] = None,
        username: Optional[str] = None,
        status: Optional[str] = None,
        start_date: Optional[datetime] = None,
        end_date: Optional[datetime] = None,
        page: int = 1,
        page_size: int = 20
    ) -> Result[Dict[str, Any]]:
        """
        搜索审计日志
        
        Args:
            action: 操作类型
            resource_type: 资源类型
            resource_id: 资源ID
            user_id: 用户ID
            username: 用户名
            status: 操作结果
            start_date: 开始日期
            end_date: 结束日期
            page: 页码
            page_size: 每页大小
            
        Returns:
            Result[Dict[str, Any]]: 包含分页信息的审计日志列表结果
        """
        try:
            page_size = page_size or 20
            page = page or 1
            self.logger.info(f"搜索审计日志: page={page}, size={page_size}, ... filters ...")
            
            logs, total = await self.audit_log_repo.search(
                self.db,
                action=action,
                resource_type=resource_type,
                resource_id=resource_id,
                user_id=user_id,
                username=username,
                status=status,
                start_date=start_date,
                end_date=end_date,
                page=page,
                page_size=page_size
            )
            
            total_pages = (total + page_size - 1) // page_size if page_size > 0 else 0
            
            paginated_response = {
                "items": [log.to_dict() for log in logs],
                "total": total,
                "page": page,
                "page_size": page_size,
                "total_pages": total_pages
            }
            
            return ResultFactory.success(paginated_response)
        except Exception as e:
            self.logger.error(f"搜索审计日志失败: {str(e)}", exc_info=True)
            return ResultFactory.error(ErrorCode.INTERNAL_ERROR, f"搜索审计日志失败: {str(e)}")
    
    async def get_latest_audit_logs(self, limit: int = 10) -> Result[List[Dict[str, Any]]]:
        """
        获取最新的审计日志
        
        Args:
            limit: 返回记录数量
            
        Returns:
            Result[List[Dict[str, Any]]]: 审计日志列表
        """
        try:
            logs = await self.audit_log_repo.get_latest_logs(self.db, limit)
            return ResultFactory.success([log.to_dict() for log in logs])
        except Exception as e:
            self.logger.error(f"获取最新审计日志失败: {str(e)}", exc_info=True)
            return ResultFactory.error(ErrorCode.INTERNAL_ERROR, f"获取最新审计日志失败: {str(e)}")
    
    async def get_resource_audit_logs(
        self, 
        resource_type: str, 
        resource_id: str
    ) -> Result[List[Dict[str, Any]]]:
        """
        获取资源的操作记录
        
        Args:
            resource_type: 资源类型
            resource_id: 资源ID
            
        Returns:
            Result[List[Dict[str, Any]]]: 审计日志列表
        """
        try:
            logs = await self.audit_log_repo.get_actions_by_resource(
                self.db, 
                resource_type, 
                resource_id
            )
            return ResultFactory.success([log.to_dict() for log in logs])
        except Exception as e:
            self.logger.error(f"获取资源操作记录失败: {str(e)}", exc_info=True)
            return ResultFactory.error(ErrorCode.INTERNAL_ERROR, f"获取资源操作记录失败: {str(e)}")
    
    async def create_audit_log(
        self,
        action: str,
        resource_type: str,
        resource_id: Optional[str] = None,
        user_id: Optional[int] = None,
        username: Optional[str] = None,
        request: Optional[Request] = None,
        details: Optional[Dict[str, Any]] = None,
        status: str = "success",
        message: Optional[str] = None
    ) -> Result[Dict[str, Any]]:
        """
        创建审计日志
        
        Args:
            action: 操作类型
            resource_type: 资源类型
            resource_id: 资源ID
            user_id: 用户ID
            username: 用户名
            request: 请求对象，用于获取IP和User-Agent
            details: 操作详情
            status: 操作结果
            message: 额外信息
            
        Returns:
            Result[Dict[str, Any]]: 创建的审计日志
        """
        try:
            # 获取IP和User-Agent
            ip_address = None
            user_agent = None
            if request:
                ip_address = request.client.host if hasattr(request, "client") else None
                user_agent = request.headers.get("user-agent")
            
            # 构建审计日志数据
            log_data = {
                "action": action,
                "resource_type": resource_type,
                "resource_id": resource_id,
                "user_id": user_id,
                "username": username,
                "ip_address": ip_address,
                "user_agent": user_agent,
                "status": status,
                "details": details,
                "message": message
            }
            
            # 创建审计日志
            log = await self.audit_log_repo.create(self.db, log_data)
            
            return ResultFactory.success(log.to_dict())
        except Exception as e:
            self.logger.error(f"创建审计日志失败: {str(e)}", exc_info=True)
            return ResultFactory.error(ErrorCode.INTERNAL_ERROR, f"创建审计日志失败: {str(e)}")

    async def get_user_logs(
        self,
        user_id: int,
        limit: int = 50
    ) -> Result[List[Dict[str, Any]]]:
        """
        获取用户的操作日志

        Args:
            user_id: 用户ID
            limit: 返回数量限制

        Returns:
            Result[List[Dict[str, Any]]]: 审计日志列表
        """
        try:
            logs = await self.audit_log_repo.get_actions_by_user(self.db, user_id, limit)
            return ResultFactory.success([log.to_dict() for log in logs])
        except Exception as e:
            self.logger.error(f"获取用户审计日志失败: {str(e)}", exc_info=True)
            return ResultFactory.error(
                ErrorCode.INTERNAL_ERROR,
                f"获取用户审计日志失败: {str(e)}"
            )

    async def get_statistics(
        self,
        start_date: Optional[datetime] = None,
        end_date: Optional[datetime] = None
    ) -> Result[Dict[str, Any]]:
        """
        获取审计日志统计信息

        Args:
            start_date: 开始日期
            end_date: 结束日期

        Returns:
            Result[Dict[str, Any]]: 统计信息
        """
        try:
            # 如果没有指定日期范围，默认统计最近30天
            if not start_date:
                start_date = datetime.now(timezone.utc) - timedelta(days=30)
            if not end_date:
                end_date = datetime.now(timezone.utc)

            # 获取统计数据
            stats = await self.audit_log_repo.get_statistics(
                self.db, start_date, end_date
            )

            return ResultFactory.success(stats)
        except Exception as e:
            self.logger.error(f"获取审计日志统计失败: {str(e)}", exc_info=True)
            return ResultFactory.error(ErrorCode.INTERNAL_ERROR, f"获取审计日志统计失败: {str(e)}")

    async def cleanup_old_logs(self, days_to_keep: int = 90) -> Result[Dict[str, Any]]:
        """
        清理旧的审计日志

        Args:
            days_to_keep: 保留天数，默认90天

        Returns:
            Result[Dict[str, Any]]: 清理结果
        """
        try:
            cutoff_date = datetime.now(timezone.utc) - timedelta(days=days_to_keep)
            deleted_count = await self.audit_log_repo.delete_old_logs(self.db, cutoff_date)

            return ResultFactory.success({
                "deleted_count": deleted_count,
                "cutoff_date": cutoff_date.isoformat(),
                "days_kept": days_to_keep
            })
        except Exception as e:
            self.logger.error(f"清理旧审计日志失败: {str(e)}", exc_info=True)
            return ResultFactory.error(ErrorCode.INTERNAL_ERROR, f"清理旧审计日志失败: {str(e)}")

    # 便捷方法，用于记录常见操作

    async def log_login(
        self,
        user_id: int,
        username: str,
        request: Optional[Request] = None,
        status: str = "success"
    ) -> Result[Dict[str, Any]]:
        """记录用户登录日志"""
        return await self.create_audit_log(
            action="login",
            resource_type="user",
            resource_id=str(user_id),
            user_id=user_id,
            username=username,
            request=request,
            status=status,
            message=f"用户 {username} 登录"
        )

    async def log_logout(
        self,
        user_id: int,
        username: str,
        request: Optional[Request] = None
    ) -> Result[Dict[str, Any]]:
        """记录用户登出日志"""
        return await self.create_audit_log(
            action="logout",
            resource_type="user",
            resource_id=str(user_id),
            user_id=user_id,
            username=username,
            request=request,
            message=f"用户 {username} 登出"
        )

    async def log_data_access(
        self,
        resource_type: str,
        resource_id: str,
        user_id: int,
        username: str,
        action: str = "read",
        request: Optional[Request] = None
    ) -> Result[Dict[str, Any]]:
        """记录数据访问日志"""
        return await self.create_audit_log(
            action=action,
            resource_type=resource_type,
            resource_id=resource_id,
            user_id=user_id,
            username=username,
            request=request,
            message=f"用户 {username} {action} {resource_type}:{resource_id}"
        )