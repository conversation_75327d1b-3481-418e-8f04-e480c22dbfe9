"""
系统模块的依赖注入函数

本模块提供系统管理相关的依赖注入函数，用于在路由和服务间共享实例。
"""

from typing import Optional, Dict, Any
from functools import lru_cache

from fastapi import Depends
from sqlalchemy.ext.asyncio import AsyncSession
from redis.asyncio import Redis

from svc.core.database import get_session_for_route
from svc.core.cache.redis import get_redis
from svc.core.config.settings import get_settings
from svc.apps.system.services.system import SystemService
from svc.apps.system.services.config import ConfigService
from svc.apps.system.services.audit_log import AuditLogService
from svc.apps.system.repositories.config import ConfigRepository
from svc.apps.system.repositories.audit_log import AuditLogRepository
from svc.core.dependencies.auth import configure_auth_dependencies

# 获取设置
settings = get_settings()


# 设置系统依赖项，注册资源类型
def setup_system_dependencies():
    """
    设置系统依赖项
    
    注册系统相关的资源类型。
    应在应用启动时调用此函数。
    """
    try:
        # 导入需要注册的模型
        from svc.apps.system.models import ConfigItem, AuditLog
        
        # 如果需要，可以在此处配置其他全局依赖项
        # 例如，可以注册这些资源到权限系统中
        
        # 注册系统模块资源类型
        configure_auth_dependencies(
            resources={
                "config_item": ConfigItem,
                "audit_log": AuditLog
            }
        )
    except ImportError:
        # 如果模型未定义或导入失败，记录日志但不中断程序
        import logging
        logger = logging.getLogger(__name__)
        logger.warning("未能注册系统模块资源类型，请检查模型是否正确定义")


# 服务实例配置字典
@lru_cache
def get_service_config() -> Dict[str, Any]:
    """获取服务配置字典
    
    返回一个配置字典，用于缓存服务实例
    
    Returns:
        Dict[str, Any]: 配置字典
    """
    return {
        "system_service": None,  # 由get_system_service初始化
        "config_service": None,  # 由get_config_service初始化
        "audit_log_service": None  # 由get_audit_log_service初始化
    }

# 全局配置字典
config = get_service_config()


# 仓库依赖项
async def get_config_repository(db: AsyncSession = Depends(get_session_for_route())) -> ConfigRepository:
    """获取配置仓库实例
    
    Args:
        db: 数据库会话
        
    Returns:
        ConfigRepository: 配置仓库实例
    """
    return ConfigRepository()


async def get_audit_log_repository(db: AsyncSession = Depends(get_session_for_route())) -> AuditLogRepository:
    """获取审计日志仓库实例
    
    Args:
        db: 数据库会话
        
    Returns:
        AuditLogRepository: 审计日志仓库实例
    """
    return AuditLogRepository()


# 服务依赖项
async def get_system_service(
    db: AsyncSession = Depends(get_session_for_route()),
    redis: Optional[Redis] = Depends(get_redis),
    config_repo: ConfigRepository = Depends(get_config_repository),
    audit_log_repo: AuditLogRepository = Depends(get_audit_log_repository)
) -> SystemService:
    """获取系统服务实例
    
    Args:
        db: 数据库会话
        redis: Redis客户端
        config_repo: 配置仓库实例
        audit_log_repo: 审计日志仓库实例
        
    Returns:
        SystemService: 系统服务实例
    """
    if config["system_service"] is None:
        config["system_service"] = SystemService(
            db=db, 
            redis=redis,
            config_repo=config_repo,
            audit_log_repo=audit_log_repo
        )
    else:
        # 更新数据库会话和Redis客户端
        config["system_service"].db = db
        config["system_service"].redis = redis
        config["system_service"].config_repo = config_repo
        config["system_service"].audit_log_repo = audit_log_repo
    
    return config["system_service"]


async def get_config_service(
    db: AsyncSession = Depends(get_session_for_route()),
    redis: Optional[Redis] = Depends(get_redis),
    config_repo: ConfigRepository = Depends(get_config_repository)
) -> ConfigService:
    """获取配置服务实例
    
    Args:
        db: 数据库会话
        redis: Redis客户端
        config_repo: 配置仓库实例
        
    Returns:
        ConfigService: 配置服务实例
    """
    if config["config_service"] is None:
        config["config_service"] = ConfigService(
            db=db,
            redis=redis,
            config_repo=config_repo
        )
    else:
        # 更新数据库会话和Redis客户端
        config["config_service"].db = db
        config["config_service"].redis = redis
        config["config_service"].config_repo = config_repo
    
    return config["config_service"]


async def get_audit_log_service(
    db: AsyncSession = Depends(get_session_for_route()),
    redis: Optional[Redis] = Depends(get_redis),
    audit_log_repo: AuditLogRepository = Depends(get_audit_log_repository)
) -> AuditLogService:
    """获取审计日志服务实例
    
    Args:
        db: 数据库会话
        redis: Redis客户端
        audit_log_repo: 审计日志仓库实例
        
    Returns:
        AuditLogService: 审计日志服务实例
    """
    if config["audit_log_service"] is None:
        config["audit_log_service"] = AuditLogService(
            db=db,
            redis=redis,
            audit_log_repo=audit_log_repo
        )
    else:
        # 更新数据库会话和Redis客户端
        config["audit_log_service"].db = db
        config["audit_log_service"].redis = redis
        config["audit_log_service"].audit_log_repo = audit_log_repo
    
    return config["audit_log_service"]

# 在模块导入时自动调用设置函数
setup_system_dependencies()
